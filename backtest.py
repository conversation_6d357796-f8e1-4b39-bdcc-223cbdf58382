# backtest.py
from __future__ import annotations
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from zoneinfo import ZoneInfo

import pandas as pd

from risk import ContractMeta, dollars_for_move

ET = ZoneInfo("America/New_York")


@dataclass
class BacktestResult:
    trades: List[Dict[str, Any]]
    equity_curve: List[Tuple[datetime, float]]
    gross_pl: float
    win_rate: float
    avg_r: float


def run_replay(
    strategy,
    minutes_df: pd.DataFrame,
    prev_day_df: Optional[pd.DataFrame],
    meta: ContractMeta,
    start_et: datetime,
    end_et: datetime,
    guardrails: Dict[str, Any],
    confidence_min: float = 0.55,
    max_contracts: int = 1,
    commission_per_contract: float = 2.04,
    slippage_ticks_entry: float = 0.25,
    slippage_ticks_exit: float = 0.25,
) -> BacktestResult:
    """
    Offline replay:
      - Evaluate on closed bar (i-1), enter on next open (i) with slippage.
      - Use PDH/PDL from prev_day_df to allow sweep signals to fire.
      - Halt not implemented here (your live guardrails handle DLL/MLL).
    minutes_df columns: ["time_et","o","h","l","c","v"]
    """
    if minutes_df is None or minutes_df.empty:
        return BacktestResult([], [], 0.0, 0.0, 0.0)

    df = minutes_df.copy()
    df = df[(df["time_et"] >= start_et) & (df["time_et"] < end_et)].reset_index(drop=True)
    if df.empty:
        return BacktestResult([], [], 0.0, 0.0, 0.0)

    # PDH/PDL from prev day
    pdh = pdl = None
    if prev_day_df is not None and not prev_day_df.empty:
        pdh = float(prev_day_df["h"].max())
        pdl = float(prev_day_df["l"].min())

    # init strategy
    strategy.min_confidence_to_trade = confidence_min
    strategy.prime(meta=meta, htf_bias=None, htf_levels=[x for x in (pdh, pdl) if x is not None])

    trades: List[Dict[str, Any]] = []
    equity_curve: List[Tuple[datetime, float]] = []
    eq = 0.0
    in_pos = False
    side = None
    entry = stop = target = None
    size = 0

    for i in range(1, len(df)):
        prev = df.iloc[i - 1]
        cur = df.iloc[i]
        equity_curve.append((cur["time_et"], eq))

        if in_pos:
            hit_stop = (cur["l"] <= stop) if side == "long" else (cur["h"] >= stop)
            hit_tgt  = (cur["h"] >= target) if side == "long" else (cur["l"] <= target)
            exit_price = None
            reason = None
            if hit_stop and hit_tgt:
                exit_price = stop; reason = "stop"
            elif hit_stop:
                exit_price = stop; reason = "stop"
            elif hit_tgt:
                exit_price = target; reason = "target"

            if exit_price is not None:
                if side == "long":
                    exit_price -= meta.tick_size * slippage_ticks_exit
                else:
                    exit_price += meta.tick_size * slippage_ticks_exit
                pl = dollars_for_move(entry, exit_price, meta, size, side) - (commission_per_contract * size)
                eq += pl
                
                # Enhanced trade record with confluence details
                trade_record = {
                    "time": cur["time_et"], 
                    "side": side, 
                    "entry": entry,
                    "exit": exit_price, 
                    "reason": reason, 
                    "size": size, 
                    "pl": pl,
                    "confidence": trade_setup.get("confidence", 0.0),
                    "confluences": ", ".join([k for k, v in trade_setup.get("breakdown", {}).items() if v > 0]),
                    "trade_reasons": "; ".join(trade_setup.get("trade_reasons", [])),
                    "commission": commission_per_contract * size,
                    "gross_pl": pl + (commission_per_contract * size)
                }
                trades.append(trade_record)
                in_pos = False
                side = None
                size = 0
                entry = stop = target = None
                trade_setup = {}
                continue

        # evaluate on closed bar (prev)
        levels = {"pdh": pdh, "pdl": pdl, "session_hi": None, "session_lo": None}
        guard = {"allow_long": guardrails.get("allow_long", True),
                 "allow_short": guardrails.get("allow_short", True),
                 "session_ok": True,
                 "dollar_risk_per_trade": guardrails.get("dollar_risk_per_trade", 0.0),
                 "max_contracts": max_contracts}
        res = strategy.evaluate(prev["c"],
                                df.iloc[:i][["o","h","l","c"]],
                                levels, meta, guard)

        if not in_pos and res.get("signal"):
            side = res["signal"]
            sug = res.get("suggested", {})
            e = float(cur["o"])
            if side == "long":
                e += meta.tick_size * slippage_ticks_entry
            else:
                e -= meta.tick_size * slippage_ticks_entry
            entry = e
            stop = float(sug.get("stop", e - meta.tick_size*8 if side == "long" else e + meta.tick_size*8))
            target = float(sug.get("target", e + meta.tick_size*8 if side == "long" else e - meta.tick_size*8))
            size = int(min(int(sug.get("qty", 1)), max_contracts))
            # pay entry commission upfront
            eq -= commission_per_contract * size
            in_pos = True
            
            # Store trade setup details for when we exit
            trade_setup = {
                "confidence": res.get("confidence", 0.0),
                "breakdown": res.get("breakdown", {}),
                "trade_reasons": res.get("trade_reasons", []),
                "analysis": res.get("analysis", {})
            }

    wins = sum(1 for t in trades if t["pl"] > 0)
    wr = wins / len(trades) if trades else 0.0
    return BacktestResult(trades, equity_curve, eq, wr, 0.0)
