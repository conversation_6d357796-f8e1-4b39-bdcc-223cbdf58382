# adaptive_polling.py
"""
Adaptive polling manager for Streamlit apps that adjusts refresh intervals
based on data availability and market conditions.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from zoneinfo import ZoneInfo
import streamlit as st

# Define timezone
ET = ZoneInfo("America/New_York")

logger = logging.getLogger(__name__)


class PollingMode(Enum):
    AGGRESSIVE = "aggressive"    # 2-3 seconds - when data is flowing
    NORMAL = "normal"           # 5-10 seconds - standard polling
    CONSERVATIVE = "conservative"  # 15-30 seconds - when data is sparse
    MINIMAL = "minimal"         # 45-60 seconds - when no data


@dataclass
class PollingConfig:
    """Configuration for adaptive polling"""
    aggressive_interval: float = 3.0      # Fast polling when data flows
    normal_interval: float = 10.0         # Standard polling
    conservative_interval: float = 15.0   # Slow polling
    minimal_interval: float = 45.0        # Very slow polling
    
    # Thresholds for mode changes
    empty_threshold_conservative: int = 3  # Empty responses to go conservative
    empty_threshold_minimal: int = 6       # Empty responses to go minimal
    
    # Market hours adjustments
    market_closed_multiplier: float = 2.0  # Slower polling when market closed
    weekend_multiplier: float = 4.0        # Much slower on weekends


class AdaptivePollingManager:
    """
    Manages adaptive polling intervals based on data availability and market conditions.
    Integrates with Streamlit's session state for persistence across reruns.
    """
    
    def __init__(self, config: PollingConfig = None):
        self.config = config or PollingConfig()
        self._init_session_state()
    
    def _init_session_state(self):
        """Initialize session state variables"""
        if 'polling_mode' not in st.session_state:
            st.session_state.polling_mode = PollingMode.NORMAL
        
        if 'empty_streak' not in st.session_state:
            st.session_state.empty_streak = 0
        
        if 'last_data_time' not in st.session_state:
            st.session_state.last_data_time = time.time()
        
        if 'last_poll_time' not in st.session_state:
            st.session_state.last_poll_time = 0
        
        if 'polling_stats' not in st.session_state:
            st.session_state.polling_stats = {
                'total_polls': 0,
                'successful_polls': 0,
                'empty_polls': 0,
                'mode_changes': 0
            }
    
    def update_data_availability(self, has_new_data: bool, data_size: int = 0):
        """Update polling mode based on data availability"""
        current_mode = st.session_state.polling_mode
        
        # Update statistics
        st.session_state.polling_stats['total_polls'] += 1
        
        if has_new_data and data_size > 0:
            # Data received - reset empty streak and go to aggressive mode
            st.session_state.empty_streak = 0
            st.session_state.last_data_time = time.time()
            st.session_state.polling_stats['successful_polls'] += 1
            
            new_mode = PollingMode.AGGRESSIVE
            if new_mode != current_mode:
                logger.info(f"Data received - switching to {new_mode.value} polling")
                st.session_state.polling_mode = new_mode
                st.session_state.polling_stats['mode_changes'] += 1
        
        else:
            # No data - increment empty streak and potentially slow down
            st.session_state.empty_streak += 1
            st.session_state.polling_stats['empty_polls'] += 1
            
            new_mode = self._calculate_mode_from_empty_streak()
            if new_mode != current_mode:
                logger.info(f"Empty streak: {st.session_state.empty_streak} - switching to {new_mode.value} polling")
                st.session_state.polling_mode = new_mode
                st.session_state.polling_stats['mode_changes'] += 1
    
    def _calculate_mode_from_empty_streak(self) -> PollingMode:
        """Calculate polling mode based on empty streak"""
        empty_streak = st.session_state.empty_streak
        
        if empty_streak >= self.config.empty_threshold_minimal:
            return PollingMode.MINIMAL
        elif empty_streak >= self.config.empty_threshold_conservative:
            return PollingMode.CONSERVATIVE
        else:
            return PollingMode.NORMAL
    
    def get_current_interval(self, market_open: bool = True) -> float:
        """Get current polling interval based on mode and market conditions"""
        mode = st.session_state.polling_mode
        
        # Base interval based on mode
        if mode == PollingMode.AGGRESSIVE:
            interval = self.config.aggressive_interval
        elif mode == PollingMode.NORMAL:
            interval = self.config.normal_interval
        elif mode == PollingMode.CONSERVATIVE:
            interval = self.config.conservative_interval
        else:  # MINIMAL
            interval = self.config.minimal_interval
        
        # Adjust for market conditions
        if not market_open:
            now = datetime.now(ET)
            if now.weekday() >= 5:  # Weekend
                interval *= self.config.weekend_multiplier
            else:
                interval *= self.config.market_closed_multiplier
        
        return interval
    
    def should_poll_now(self, market_open: bool = True) -> bool:
        """Check if enough time has passed to poll again"""
        now = time.time()
        last_poll = st.session_state.last_poll_time
        interval = self.get_current_interval(market_open)
        
        return (now - last_poll) >= interval
    
    def mark_poll_attempt(self):
        """Mark that a poll attempt was made"""
        st.session_state.last_poll_time = time.time()
    
    def get_time_until_next_poll(self, market_open: bool = True) -> float:
        """Get seconds until next poll should happen"""
        now = time.time()
        last_poll = st.session_state.last_poll_time
        interval = self.get_current_interval(market_open)
        
        elapsed = now - last_poll
        return max(0, interval - elapsed)
    
    def get_polling_status(self) -> Dict[str, Any]:
        """Get current polling status for display"""
        now = time.time()
        last_data_age = now - st.session_state.last_data_time
        last_poll_age = now - st.session_state.last_poll_time
        
        return {
            'mode': st.session_state.polling_mode.value,
            'empty_streak': st.session_state.empty_streak,
            'last_data_age_seconds': last_data_age,
            'last_poll_age_seconds': last_poll_age,
            'current_interval': self.get_current_interval(),
            'stats': st.session_state.polling_stats.copy()
        }
    
    def reset_polling_state(self):
        """Reset polling state to initial values"""
        st.session_state.polling_mode = PollingMode.NORMAL
        st.session_state.empty_streak = 0
        st.session_state.last_data_time = time.time()
        st.session_state.last_poll_time = 0
        st.session_state.polling_stats = {
            'total_polls': 0,
            'successful_polls': 0,
            'empty_polls': 0,
            'mode_changes': 0
        }
        logger.info("Polling state reset")


class StreamlitPollingIntegration:
    """
    Integrates adaptive polling with Streamlit's auto-refresh mechanism.
    Provides a clean interface for managing polling in Streamlit apps.
    """
    
    def __init__(self, polling_manager: AdaptivePollingManager = None):
        self.manager = polling_manager or AdaptivePollingManager()
    
    def setup_auto_refresh(self, market_open: bool = True) -> bool:
        """
        Setup auto-refresh based on adaptive polling.
        Returns True if a refresh should happen now.
        """
        if not self.manager.should_poll_now(market_open):
            # Show countdown until next refresh
            time_until_next = self.manager.get_time_until_next_poll(market_open)
            status = self.manager.get_polling_status()
            
            st.sidebar.info(
                f"🔄 **Polling Status**\n\n"
                f"Mode: {status['mode'].title()}\n"
                f"Next refresh: {time_until_next:.0f}s\n"
                f"Empty streak: {status['empty_streak']}\n"
                f"Success rate: {status['stats']['successful_polls']}/{status['stats']['total_polls']}"
            )
            return False
        
        # Time to refresh
        self.manager.mark_poll_attempt()
        return True
    
    def handle_data_result(self, data_result: Any, data_type: str = "data") -> bool:
        """
        Handle the result of a data fetch and update polling accordingly.
        Returns True if data was received.
        """
        has_data = False
        data_size = 0
        
        if data_result is not None:
            if hasattr(data_result, '__len__'):
                data_size = len(data_result)
                has_data = data_size > 0
            elif hasattr(data_result, 'empty'):
                # Pandas DataFrame
                has_data = not data_result.empty
                data_size = len(data_result) if has_data else 0
            else:
                # Assume any non-None result is data
                has_data = True
                data_size = 1
        
        self.manager.update_data_availability(has_data, data_size)
        
        if has_data:
            logger.debug(f"Received {data_size} {data_type} items")
        else:
            logger.debug(f"No {data_type} received")
        
        return has_data
    
    def show_polling_debug_info(self):
        """Show detailed polling information for debugging"""
        status = self.manager.get_polling_status()
        
        with st.expander("🔍 Polling Debug Info"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**Current Status:**")
                st.write(f"Mode: {status['mode'].title()}")
                st.write(f"Empty streak: {status['empty_streak']}")
                st.write(f"Current interval: {status['current_interval']:.1f}s")
                st.write(f"Last data: {status['last_data_age_seconds']:.0f}s ago")
            
            with col2:
                st.write("**Statistics:**")
                stats = status['stats']
                st.write(f"Total polls: {stats['total_polls']}")
                st.write(f"Successful: {stats['successful_polls']}")
                st.write(f"Empty: {stats['empty_polls']}")
                st.write(f"Mode changes: {stats['mode_changes']}")
                
                if stats['total_polls'] > 0:
                    success_rate = stats['successful_polls'] / stats['total_polls'] * 100
                    st.write(f"Success rate: {success_rate:.1f}%")
            
            if st.button("Reset Polling State"):
                self.manager.reset_polling_state()
                st.success("Polling state reset!")
                st.rerun()


# Global polling integration instance
_polling_integration = None


def get_polling_integration() -> StreamlitPollingIntegration:
    """Get or create the global polling integration instance"""
    global _polling_integration
    if _polling_integration is None:
        _polling_integration = StreamlitPollingIntegration()
    return _polling_integration
