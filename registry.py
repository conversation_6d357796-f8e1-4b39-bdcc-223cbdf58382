"""
Strategy registry for the application.
"""
from __future__ import annotations
from typing import List, Dict, Callable, Any

_REG: Dict[str, Callable[..., Any]] = {}

def register(name: str, builder: Callable[..., Any]) -> None:
    _REG[name] = builder

def _register_defaults() -> None:
    # Lazy imports to avoid heavy deps at import time
    from enhanced_smc import build as smc_build
    register("Enhanced SMC", smc_build)

def available() -> List[str]:
    if not _REG:
        _register_defaults()
    return sorted(_REG.keys())

def create(name: str, **kwargs) -> Any:
    if not _REG:
        _register_defaults()
    if name not in _REG:
        raise ValueError(f"Unknown strategy: {name}")
    return _REG[name](**kwargs)
