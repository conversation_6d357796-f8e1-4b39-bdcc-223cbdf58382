import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    def __init__(self):
        self.base_url = os.getenv("PROJECTX_BASE_URL", "https://gateway-api-demo.s2f.projectx.com")
        self.username = os.getenv("PROJECTX_USERNAME", "kbendish")
        self.api_key = os.getenv("PROJECTX_API_KEY", "KUN6wMUxssLc1nLeSRL7iYtAYtrJAvijjyoLMCV5xDo=")
        
        # Debug: Check if credentials are loaded
        print(f"Username: {self.username}")
        print(f"API Key: {self.api_key[:10]}..." if self.api_key else "No API Key")
        print(f"Base URL: {self.base_url}")

settings = Settings()

