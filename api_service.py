# api_service.py
"""
API service layer that integrates the optimized API client with Streamlit caching.
Provides a unified interface for all API operations with proper caching and coalescing.
"""

import logging
import time
from datetime import datetime, timezone, timedelta, date
from typing import Dict, List, Any, Optional, Tuple, Union
from zoneinfo import ZoneInfo
import pandas as pd
import streamlit as st

from api_client import SyncAPIClient
from broker_adapter import BrokerAdapter  # For compatibility during transition

# Define timezone and utility functions
ET = ZoneInfo("America/New_York")

def et_midnight(d: date) -> datetime:
    """Convert a date to midnight ET"""
    return datetime(d.year, d.month, d.day, 0, 0, 0, tzinfo=ET)

logger = logging.getLogger(__name__)

# Global client instance
_api_client = None


def get_api_client(base_url: str = None, token: str = None) -> SyncAPIClient:
    """Get or create the global API client instance"""
    global _api_client
    if _api_client is None:
        if not base_url or not token:
            # Fall back to environment variables
            from settings import settings
            base_url = base_url or settings.PROJECTX_BASE_URL
            token = token or settings.PROJECTX_TOKEN
        
        logger.info(f"Creating new API client for {base_url}")
        _api_client = SyncAPIClient(base_url, token)
    
    return _api_client


@st.cache_data(ttl=600)  # 10 minutes
def get_contracts(search_text: str = "", live: bool = False) -> List[Dict[str, Any]]:
    """Get contracts with 10-minute Streamlit cache"""
    client = get_api_client()
    logger.info(f"Fetching contracts for '{search_text}' (live={live})")
    return client.search_contracts(search_text, live)


@st.cache_data(ttl=15)  # 15 seconds
def get_accounts(only_active: bool = True) -> List[Dict[str, Any]]:
    """Get accounts with 15-second Streamlit cache"""
    client = get_api_client()
    logger.info("Fetching accounts")
    return client.search_accounts(only_active)


@st.cache_data(ttl=15)  # 15 seconds
def get_account_summary(account_id: str) -> Dict[str, Any]:
    """Get account summary with 15-second Streamlit cache"""
    accounts = get_accounts()
    for account in accounts:
        if account.get("id") == account_id:
            return account
    return {}


@st.cache_data(ttl=10)  # 10 seconds
def get_positions(account_id: str) -> List[Dict[str, Any]]:
    """Get positions with 10-second Streamlit cache"""
    client = get_api_client()
    logger.info(f"Fetching positions for account {account_id}")
    return client.get_positions(account_id)


@st.cache_data(ttl=10)  # 10 seconds
def get_orders(account_id: str) -> List[Dict[str, Any]]:
    """Get orders with 10-second Streamlit cache"""
    client = get_api_client()
    logger.info(f"Fetching orders for account {account_id}")
    return client.get_orders(account_id)


@st.cache_data(ttl=10)  # 10 seconds
def get_trades(account_id: str, start_timestamp: str = None, 
              start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
    """Get trades with 10-second Streamlit cache"""
    client = get_api_client()
    logger.info(f"Fetching trades for account {account_id}")
    return client.get_trades(account_id, start_timestamp, start_date, end_date)


@st.cache_data(ttl=60)  # 1 minute
def get_minute_bars_range(contract_id: str, start_day: date, end_day: date, live: bool) -> pd.DataFrame:
    """
    Fetch minute bars for a date range with a single API call instead of per-day calls.
    This dramatically reduces API load and prevents 429 rate limiting.
    """
    client = get_api_client()
    
    start_dt = et_midnight(start_day)
    end_dt = et_midnight(end_day + timedelta(days=1))
    
    if live:
        # Clamp to now for live requests
        end_dt = min(end_dt, datetime.now(ET))
    
    # Convert to UTC ISO for API call
    start_iso = start_dt.astimezone(timezone.utc).isoformat()
    end_iso = end_dt.astimezone(timezone.utc).isoformat()
    
    data_type = "LIVE" if live else "HISTORICAL"
    logger.info(f"Fetching {data_type} bars for {contract_id} from {start_dt} to {end_dt}")
    
    try:
        if live:
            bars = client.get_live_bars(
                contract_id=contract_id,
                start_iso=start_iso,
                end_iso=end_iso,
                unit=2,  # Minutes
                unit_number=1,  # 1-minute bars
                limit=20000
            )
        else:
            bars = client.get_historical_bars(
                contract_id=contract_id,
                start_iso=start_iso,
                end_iso=end_iso,
                unit=2,  # Minutes
                unit_number=1,  # 1-minute bars
                limit=20000
            )
        
        if not bars:
            logger.warning(f"No {data_type} bars returned for {contract_id} in range")
            return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])
        
        # Process bars into DataFrame
        df = pd.DataFrame(bars)
        
        # Standardize column names
        rename_map = {
            "timestamp": "time_et",
            "open": "o",
            "high": "h",
            "low": "l",
            "close": "c",
            "volume": "v"
        }
        
        # Map API field names to our standard names
        for api_name, std_name in rename_map.items():
            if api_name in df.columns and std_name not in df.columns:
                df[std_name] = df[api_name]
        
        # Ensure all required columns exist
        required_cols = ["time_et", "o", "h", "l", "c", "v"]
        for col in required_cols:
            if col not in df.columns:
                if col == "v":  # Volume might be missing
                    df[col] = 0
                else:
                    raise ValueError(f"Required column {col} missing from API response")
        
        # Convert timestamp to ET timezone
        df['time_et'] = pd.to_datetime(df['time_et'])
        if df['time_et'].dt.tz is None:
            df['time_et'] = df['time_et'].dt.tz_localize('UTC').dt.tz_convert('America/New_York')
        
        return df[required_cols]
        
    except Exception as e:
        logger.error(f"Error fetching bars range: {e}")
        return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])


@st.cache_data
def get_minute_bars_for_day(contract_id: str, day: date, live: bool) -> pd.DataFrame:
    """Get minute bars for a specific day with caching"""
    return get_minute_bars_range(contract_id, day, day, live)


def get_next_poll_interval() -> float:
    """Get the next polling interval based on data availability"""
    client = get_api_client()
    return client.get_next_live_poll_interval()


def clear_cache(pattern: str = None):
    """Clear both Streamlit cache and internal client cache"""
    # Clear Streamlit cache
    st.cache_data.clear()
    
    # Clear internal client cache
    client = get_api_client()
    client.clear_cache(pattern)
    
    logger.info(f"Cleared caches{f' matching {pattern}' if pattern else ''}")


@st.cache_data(ttl=30)  # 30 seconds
def check_market_status() -> Tuple[str, bool]:
    """Check market status with caching to avoid duplicate calls"""
    # This would typically call an API endpoint, but for now we'll use time-based logic
    now = datetime.now(ET)

    # TopstepX market hours (Sunday 6PM ET to Friday 5PM ET)
    weekday = now.weekday()  # 0=Monday, 6=Sunday
    hour = now.hour

    if weekday == 6 and hour >= 18:  # Sunday after 6PM
        return "OPEN", True
    elif 0 <= weekday <= 4:  # Monday-Friday
        if weekday == 4 and hour >= 17:  # Friday after 5PM
            return "CLOSED", False
        else:
            return "OPEN", True
    else:
        return "CLOSED", False


def calculate_pnl(trades: List[Dict[str, Any]]) -> Dict[str, float]:
    """Calculate P&L from trades with caching"""
    if not trades:
        return {"realized_pnl": 0.0, "unrealized_pnl": 0.0, "total_pnl": 0.0}

    realized_pnl = 0.0
    for trade in trades:
        # Extract P&L from trade data
        pnl = trade.get("pnl", 0.0) or trade.get("realizedPnl", 0.0)
        if isinstance(pnl, (int, float)):
            realized_pnl += float(pnl)

    return {
        "realized_pnl": realized_pnl,
        "unrealized_pnl": 0.0,  # Would need position data to calculate
        "total_pnl": realized_pnl
    }


def get_comprehensive_pnl(account_id: str) -> Dict[str, Any]:
    """Get comprehensive P&L data with caching"""
    # Get today's trades
    today = datetime.now(ET).date()
    start_date = today.strftime("%Y-%m-%d")
    end_date = today.strftime("%Y-%m-%d")

    trades = get_trades(account_id, start_date=start_date, end_date=end_date)
    pnl_data = calculate_pnl(trades)

    return {
        "account_id": account_id,
        "date": start_date,
        "trades_count": len(trades),
        "realized_pnl": pnl_data["realized_pnl"],
        "unrealized_pnl": pnl_data["unrealized_pnl"],
        "total_pnl": pnl_data["total_pnl"],
        "trades": trades
    }


class DataRefreshManager:
    """Manages data refresh cycles to ensure single source of truth per cycle"""

    def __init__(self):
        self.last_refresh = {}
        self.refresh_interval = 5.0  # Minimum seconds between refreshes

    def should_refresh(self, data_type: str) -> bool:
        """Check if data type should be refreshed"""
        now = time.time()
        last = self.last_refresh.get(data_type, 0)
        return (now - last) >= self.refresh_interval

    def mark_refreshed(self, data_type: str):
        """Mark data type as refreshed"""
        self.last_refresh[data_type] = time.time()

    def get_all_account_data(self, account_id: str) -> Dict[str, Any]:
        """Get all account-related data in a single refresh cycle"""
        if not self.should_refresh("account_data"):
            # Return cached data if available
            if hasattr(st.session_state, 'last_account_data'):
                return st.session_state.last_account_data

        logger.info(f"Refreshing all account data for {account_id}")

        # Single refresh cycle - get all data at once
        account_summary = get_account_summary(account_id)
        positions = get_positions(account_id)
        orders = get_orders(account_id)
        pnl_data = get_comprehensive_pnl(account_id)

        result = {
            "account": account_summary,
            "positions": positions,
            "orders": orders,
            "pnl": pnl_data,
            "timestamp": datetime.now(ET).isoformat()
        }

        # Cache in session state
        st.session_state.last_account_data = result
        self.mark_refreshed("account_data")

        return result


# Global refresh manager instance
_refresh_manager = DataRefreshManager()


def get_refresh_manager() -> DataRefreshManager:
    """Get the global refresh manager instance"""
    return _refresh_manager


def ensure_ohlcv(df: pd.DataFrame) -> pd.DataFrame:
    """Ensure DataFrame has all required OHLCV columns"""
    if df.empty:
        return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])

    required_cols = ["time_et", "o", "h", "l", "c", "v"]
    for col in required_cols:
        if col not in df.columns:
            if col == "v":
                df[col] = 0  # Default volume to 0
            else:
                logger.warning(f"Missing required column {col} in DataFrame")
                df[col] = 0

    return df[required_cols]
