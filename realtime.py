"""
SignalR-based real-time data handler for TopstepX
"""
from __future__ import annotations

from typing import Callable, Optional, Dict, Any
from signalrcore.hub_connection_builder import HubConnectionBuilder
import json

class ProjectXRealtime:
    """
    Minimal SignalR client for ProjectX-style hubs.
    Adjust hub URL and event names to match your environment.
    """
    def __init__(self, hub_url: str, token: str):
        self.hub_url = hub_url
        self.token = token
        self.hub = None
        self.connected = False

        self.quotes: Dict[str, Any] = {}
        self.orders: Dict[str, Any] = {}
        self.positions: Dict[str, Any] = {}

        self.on_quote_cb: Optional[Callable[[dict], None]] = None
        self.on_order_cb: Optional[Callable[[dict], None]] = None
        self.on_position_cb: Optional[Callable[[dict], None]] = None

    def start(self):
        try:
            self.hub = (HubConnectionBuilder()
                        .with_url(self.hub_url, options={
                            "headers": {"Authorization": f"Bearer {self.token}"},
                            "verify_ssl": False  # For development
                        })
                        .with_automatic_reconnect({
                            "type": "raw",
                            "keep_alive_interval": 10,
                            "reconnect_interval": 5,
                            "max_attempts": 3
                        })
                        .build())
            
            self.hub.on_open(self.on_open)
            self.hub.on_close(self.on_close)
            self.hub.on_error(self.on_error)

            # Event names must match your server hub contracts
            self.hub.on("Quote", self._handle_quote)
            self.hub.on("OrderUpdate", self._handle_order)
            self.hub.on("PositionUpdate", self._handle_position)

            self.hub.start()
            
            # Wait a moment to see if connection establishes
            import time
            time.sleep(1)
            
        except Exception as e:
            print(f"Failed to start SignalR connection: {e}")
            self.connected = False
            raise

    def on_open(self):
        print("SignalR connection opened")
        self.connected = True

    def on_close(self, *args):
        print("SignalR connection closed")
        self.connected = False

    def on_error(self, error):
        print(f"SignalR error: {error}")
        self.connected = False

    def stop(self):
        try:
            if self.hub:
                self.hub.stop()
        finally:
            self.hub = None

    def subscribe_quotes(self, contract_id: str):
        self.hub.send("SubscribeQuote", [contract_id])

    def unsubscribe_quotes(self, contract_id: str):
        self.hub.send("UnsubscribeQuote", [contract_id])

    def _handle_quote(self, msg: str):
        try:
            data = json.loads(msg[0])
            cid = str(data.get("contractId"))
            self.quotes[cid] = data
            if self.on_quote_cb:
                self.on_quote_cb(data)
        except Exception as e:
            print(f"Error handling quote: {e}")

    def _handle_order(self, msg: str):
        try:
            data = json.loads(msg[0])
            oid = str(data.get("orderId"))
            self.orders[oid] = data
            if self.on_order_cb:
                self.on_order_cb(data)
        except Exception as e:
            print(f"Error handling order: {e}")

    def _handle_position(self, msg: str):
        try:
            data = json.loads(msg[0])
            cid = str(data.get("contractId"))
            self.positions[cid] = data
            if self.on_position_cb:
                self.on_position_cb(data)
        except Exception as e:
            print(f"Error handling position: {e}")
