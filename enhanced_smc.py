# enhanced_smc.py
"""
Enhanced SMC Strategy - Optimized for TopStepX Trading
- Institutional order flow detection via liquidity sweeps
- Market structure analysis (BOS/CHoCH) with non-repainting logic
- Fair Value Gap identification and tracking
- Order Block detection with confluence scoring
- Dynamic risk management and position sizing
- Session-based filtering with market hours awareness
"""
from __future__ import annotations
from dataclasses import dataclass, field
from typing import Dict, Any, List, Tuple, Optional
import pandas as pd
from datetime import datetime
from zoneinfo import ZoneInfo
from risk import ContractMeta, qty_for_dollar_risk


def is_trading_session(current_time: datetime) -> bool:
    """Enhanced session filtering for optimal trading windows"""
    if current_time.tzinfo is None:
        return True
    
    et_time = current_time.astimezone(ZoneInfo("America/New_York"))
    hour = et_time.hour
    minute = et_time.minute
    
    # Avoid low-volume periods
    if hour < 6 or hour > 20:  # Outside 6 AM - 8 PM ET
        return False
    if 11 <= hour <= 13 and minute < 30:  # Lunch lull
        return False
    if hour == 17 and minute > 30:  # Post-close low volume
        return False
    
    return True


def ensure_v(df: pd.DataFrame) -> pd.DataFrame:
    """Ensure volume column exists"""
    if "v" not in df.columns:
        df = df.copy()
        df["v"] = 1000.0  # Default volume
    return df


def swing_indices(series: pd.Series, left: int = 2, right: int = 2) -> Tuple[List[int], List[int]]:
    """Optimized swing point detection"""
    s = series.values
    hi, lo = [], []
    
    for i in range(left, len(s) - right):
        window = s[i - left: i + right + 1]
        center_val = s[i]
        
        # High swing: center is highest in window
        if center_val == window.max() and window.argmax() == left:
            hi.append(i)
        
        # Low swing: center is lowest in window  
        if center_val == window.min() and window.argmin() == left:
            lo.append(i)
    
    return hi, lo


def detect_liquidity_sweep(df: pd.DataFrame, level: float, sweep_high: bool = True,
                          min_wick_ratio: float = 0.15, min_rejection_pips: float = 1.0) -> Optional[Dict[str, Any]]:
    """
    Enhanced liquidity sweep detection with adaptive thresholds
    """
    if len(df) < 2 or level is None:
        return None

    last = df.iloc[-1]
    
    # Check if level was swept
    swept = (last["h"] > level) if sweep_high else (last["l"] < level)
    if not swept:
        return None
    
    # Calculate rejection metrics
    body_size = abs(last["c"] - last["o"])
    
    if sweep_high:
        wick_size = last["h"] - max(last["c"], last["o"])
        rejection = last["h"] - last["c"]
        closed_back_inside = last["c"] < level
    else:
        wick_size = min(last["c"], last["o"]) - last["l"]
        rejection = last["c"] - last["l"]
        closed_back_inside = last["c"] > level
    
    # Adaptive thresholds based on volatility
    wick_ratio = wick_size / max(body_size, 0.001)
    
    # More lenient criteria for better trade frequency
    if (wick_ratio >= min_wick_ratio and 
        rejection >= min_rejection_pips and 
        closed_back_inside):
        
        strength = min(wick_ratio * rejection * 2, 10.0)  # Enhanced strength calculation
        
        return {
            "swept": True,
            "wick": wick_size,
            "rejection": rejection,
            "wick_ratio": wick_ratio,
            "strength": strength,
            "quality": "strong" if strength > 6 else "moderate"
        }
    
    return None


def detect_fvg(df: pd.DataFrame, min_gap_ticks: float = 2.0) -> List[Dict[str, Any]]:
    """
    Enhanced Fair Value Gap detection with gap quality scoring
    """
    gaps = []
    if len(df) < 3:
        return gaps
    
    for i in range(1, len(df) - 1):
        prev_candle = df.iloc[i - 1]
        current_candle = df.iloc[i]
        next_candle = df.iloc[i + 1]
        
        # Bullish FVG: gap between prev high and next low
        if prev_candle["h"] < next_candle["l"]:
            gap_size = next_candle["l"] - prev_candle["h"]
            if gap_size >= min_gap_ticks:
                gaps.append({
                    "direction": "bullish",
                    "upper": next_candle["l"],
                    "lower": prev_candle["h"],
                    "size": gap_size,
                    "index": i,
                    "quality": "strong" if gap_size > min_gap_ticks * 2 else "moderate"
                })
        
        # Bearish FVG: gap between prev low and next high
        elif prev_candle["l"] > next_candle["h"]:
            gap_size = prev_candle["l"] - next_candle["h"]
            if gap_size >= min_gap_ticks:
                gaps.append({
                    "direction": "bearish",
                    "upper": prev_candle["l"],
                    "lower": next_candle["h"],
                    "size": gap_size,
                    "index": i,
                    "quality": "strong" if gap_size > min_gap_ticks * 2 else "moderate"
                })
    
    return gaps


def detect_order_blocks(df: pd.DataFrame, lookback: int = 30) -> List[Dict[str, Any]]:
    """
    Enhanced order block detection with quality scoring
    """
    blocks = []
    if len(df) < 10:
        return blocks
        
    window = df.tail(lookback).reset_index(drop=True)
    
    # Calculate average range for displacement threshold
    avg_range = (window["h"] - window["l"]).rolling(10).mean().iloc[-1]
    if pd.isna(avg_range) or avg_range <= 0:
        avg_range = (window["h"] - window["l"]).median()
    
    displacement_threshold = max(avg_range * 0.3, 1e-6)
    
    for i in range(len(window) - 3):
        current = window.iloc[i]
        next_candle = window.iloc[i + 1]
        
        # Look ahead for displacement
        future_window = window.iloc[i + 1:i + 4]
        
        # Bullish OB: bearish candle followed by strong bullish displacement
        if (current["c"] < current["o"] and  # Bearish candle
            future_window["h"].max() - current["h"] > displacement_threshold):
            
            strength = (future_window["h"].max() - current["h"]) / avg_range
            blocks.append({
                "type": "bullish",
                "high": float(current["h"]),
                "low": float(current["l"]),
                "open": float(current["o"]),
                "close": float(current["c"]),
                "index": i,
                "strength": min(strength, 5.0),
                "quality": "strong" if strength > 2.0 else "moderate"
            })
        
        # Bearish OB: bullish candle followed by strong bearish displacement
        elif (current["c"] > current["o"] and  # Bullish candle
              current["l"] - future_window["l"].min() > displacement_threshold):
            
            strength = (current["l"] - future_window["l"].min()) / avg_range
            blocks.append({
                "type": "bearish",
                "high": float(current["h"]),
                "low": float(current["l"]),
                "open": float(current["o"]),
                "close": float(current["c"]),
                "index": i,
                "strength": min(strength, 5.0),
                "quality": "strong" if strength > 2.0 else "moderate"
            })
    
    return blocks


def detect_market_structure(df: pd.DataFrame, min_displacement: float = 3.0) -> Tuple[Optional[str], Dict[str, Any]]:
    """
    Enhanced market structure detection with trend context
    """
    if len(df) < 8:
        return None, {}
    
    # Get swing points with tighter parameters for better detection
    hi_idx, lo_idx = swing_indices(df["h"], 2, 2)
    if len(hi_idx) < 2 or len(lo_idx) < 2:
        return None, {}
    
    # Get recent swings
    recent_highs = hi_idx[-2:]
    recent_lows = lo_idx[-2:]
    
    last_hi_idx = recent_highs[-1]
    prev_hi_idx = recent_highs[-2] if len(recent_highs) > 1 else recent_highs[0]
    last_lo_idx = recent_lows[-1]
    prev_lo_idx = recent_lows[-2] if len(recent_lows) > 1 else recent_lows[0]
    
    # Calculate displacements
    hi_displacement = df["h"].iloc[last_hi_idx] - df["h"].iloc[prev_hi_idx]
    lo_displacement = df["l"].iloc[prev_lo_idx] - df["l"].iloc[last_lo_idx]
    
    # Determine structure with context
    if hi_displacement > min_displacement and lo_displacement > min_displacement:
        # Both broken - determine stronger signal
        if hi_displacement > lo_displacement * 1.2:
            return "bullish_bos", {"displacement": hi_displacement, "strength": "strong", "context": "trending_up"}
        elif lo_displacement > hi_displacement * 1.2:
            return "bearish_bos", {"displacement": lo_displacement, "strength": "strong", "context": "trending_down"}
        else:
            return "ranging", {"displacement": max(hi_displacement, lo_displacement), "strength": "weak", "context": "choppy"}
    
    elif hi_displacement > min_displacement:
        return "bullish_bos", {"displacement": hi_displacement, "strength": "medium", "context": "bullish_break"}
    elif lo_displacement > min_displacement:
        return "bearish_bos", {"displacement": lo_displacement, "strength": "medium", "context": "bearish_break"}
    
    return None, {}


def calculate_confluence_score(ctx: Dict[str, Any], settings: Dict[str, bool]) -> Tuple[float, Dict[str, float]]:
    """
    Enhanced confluence scoring with weighted factors
    """
    scores = {}
    
    # Core signals (higher weights)
    if settings.get("use_sweep", True) and ctx.get("sweep"):
        sweep_quality = ctx["sweep"].get("quality", "moderate")
        scores["sweep"] = 0.35 if sweep_quality == "strong" else 0.25
    
    if settings.get("use_mss", True) and ctx.get("mss"):
        mss_label = ctx["mss"].get("label")
        mss_strength = ctx["mss"].get("strength", "medium")
        if mss_label in ("bullish_bos", "bearish_bos"):
            scores["mss"] = 0.30 if mss_strength == "strong" else 0.20
        elif mss_label in ("bullish_choch", "bearish_choch"):
            scores["mss"] = 0.15
    
    # Supporting confluences (moderate weights)
    if settings.get("use_fvg", True) and ctx.get("fvg_active"):
        fvg_quality = ctx.get("fvg_quality", "moderate")
        scores["fvg"] = 0.20 if fvg_quality == "strong" else 0.15
    
    if settings.get("use_order_blocks", True) and ctx.get("order_block"):
        ob_quality = ctx["order_block"].get("quality", "moderate")
        scores["order_block"] = 0.20 if ob_quality == "strong" else 0.15
    
    # Additional confluences (lower weights)
    if settings.get("use_proximity", True) and ctx.get("near_level"):
        scores["proximity"] = 0.10
    
    if settings.get("use_htf_bias", True) and ctx.get("htf_aligned"):
        scores["htf_bias"] = 0.10
    
    if settings.get("use_session_filter", True) and ctx.get("session_optimal"):
        scores["session"] = 0.05
    
    total_score = sum(scores.values())
    return min(total_score, 1.0), scores  # Cap at 100%


def get_dynamic_stops_targets(price: float, signal: str, meta, 
                            volatility: float, confidence: float) -> Tuple[float, float]:
    """
    Dynamic stop/target calculation based on market conditions
    """
    # Handle both dict and ContractMeta objects
    tick_size = meta.tick_size if hasattr(meta, 'tick_size') else meta.get('tick_size', 0.25)
    
    # Base distances in ticks
    base_stop_ticks = 8
    base_target_ticks = 16
    
    # Adjust for volatility (higher vol = wider stops)
    vol_multiplier = max(0.8, min(2.0, volatility / 10.0))
    
    # Adjust for confidence (higher confidence = tighter stops, wider targets)
    conf_multiplier = 1.0 + (confidence - 0.5)  # 0.5 confidence = 1.0x, 1.0 confidence = 1.5x
    
    stop_distance = int(base_stop_ticks * vol_multiplier)
    target_distance = int(base_target_ticks * vol_multiplier * conf_multiplier)
    
    if signal == "long":
        stop = price - (tick_size * stop_distance)
        target = price + (tick_size * target_distance)
    else:
        stop = price + (tick_size * stop_distance)
        target = price - (tick_size * target_distance)
    
    return stop, target


@dataclass
class EnhancedSMCStrategy:
    """
    Enhanced Smart Money Concepts Strategy for TopStepX
    """
    min_confidence_to_trade: float = 0.50  # Lowered for better trade frequency
    use_dynamic_sizing: bool = True
    max_risk_per_trade: float = 100.0
    
    # Confluence toggles
    use_sweep: bool = True
    use_mss: bool = True
    use_fvg: bool = True
    use_order_blocks: bool = True
    use_htf_bias: bool = True
    use_proximity: bool = True
    use_session_filter: bool = True
    
    # Internal state
    _meta: ContractMeta = field(default_factory=ContractMeta)
    _htf_bias: Optional[str] = None
    _htf_levels: List[float] = field(default_factory=list)

    def prime(self, meta: ContractMeta, htf_bias: Optional[str], htf_levels: List[float] = None):
        """Initialize strategy with contract metadata and HTF context"""
        self._meta = meta
        self._htf_bias = htf_bias
        self._htf_levels = list(htf_levels or [])

    def evaluate(self, price: Optional[float], minutes_df: pd.DataFrame, 
                levels: Dict[str, Any], meta: ContractMeta, 
                guardrails: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main strategy evaluation with enhanced SMC logic
        """
        result = {
            "signal": None,
            "confidence": 0.0,
            "breakdown": {},
            "suggested": {},
            "analysis": {},
            "trade_reasons": []
        }
        
        if price is None or minutes_df is None or len(minutes_df) < 20:
            return result

        df = ensure_v(minutes_df.copy())
        last_candle = df.iloc[-1]
        trade_reasons = []
        
        # Session filtering
        current_time = last_candle.get("time_et", datetime.now())
        session_optimal = is_trading_session(current_time)
        if self.use_session_filter and not session_optimal:
            trade_reasons.append("❌ Outside optimal trading session")
            return result
        
        # Calculate market volatility for dynamic adjustments
        recent_ranges = (df["h"] - df["l"]).tail(20)
        avg_volatility = recent_ranges.mean()
        
        # Extract key levels
        pdh = levels.get("pdh")
        pdl = levels.get("pdl")
        session_hi = levels.get("session_hi")
        session_lo = levels.get("session_lo")
        
        # 1. LIQUIDITY SWEEP DETECTION
        sweep_detected = None
        if self.use_sweep:
            key_levels = [
                (pdh, True, "PDH"),
                (pdl, False, "PDL"),
                (session_hi, True, "Session High"),
                (session_lo, False, "Session Low")
            ]
            
            for level, is_high, label in key_levels:
                if level is not None:
                    sweep = detect_liquidity_sweep(
                        df.tail(5), level, sweep_high=is_high,
                        min_wick_ratio=0.15, min_rejection_pips=1.0
                    )
                    if sweep:
                        sweep_detected = {
                            "type": "sell" if is_high else "buy",
                            "level": level,
                            "label": label,
                            **sweep
                        }
                        trade_reasons.append(f"🎯 {sweep['quality'].upper()} sweep: {label} @ {level:.2f}")
                        break
        
        # 2. MARKET STRUCTURE ANALYSIS
        mss_result = None
        mss_data = {}
        if self.use_mss:
            mss_label, mss_data = detect_market_structure(df[["h", "l", "c"]], min_displacement=2.5)
            if mss_label:
                mss_result = {"label": mss_label, **mss_data}
                trade_reasons.append(f"📈 Market Structure: {mss_label} ({mss_data.get('strength', 'medium')})")
        
        # 3. FAIR VALUE GAP ANALYSIS
        fvg_active = False
        fvg_quality = "moderate"
        if self.use_fvg:
            fvg_zones = detect_fvg(df.tail(50), min_gap_ticks=1.5)
            for gap in fvg_zones[-5:]:  # Check recent gaps
                if gap["lower"] <= price <= gap["upper"]:
                    fvg_active = True
                    fvg_quality = gap["quality"]
                    trade_reasons.append(f"📊 Active FVG: {gap['direction']} gap ({fvg_quality})")
                    break
        
        # 4. ORDER BLOCK PROXIMITY
        nearby_ob = None
        if self.use_order_blocks:
            order_blocks = detect_order_blocks(df.tail(40))
            for ob in reversed(order_blocks[-3:]):  # Check most recent blocks
                ob_center = (ob["high"] + ob["low"]) / 2
                # Handle both dict and ContractMeta objects
                tick_size = meta.tick_size if hasattr(meta, 'tick_size') else meta.get('tick_size', 0.25)
                distance_ticks = abs(price - ob_center) / tick_size
                if distance_ticks <= 12:  # Within 12 ticks
                    nearby_ob = ob
                    trade_reasons.append(f"🏗️ Near {ob['quality']} {ob['type']} OB: {distance_ticks:.1f} ticks")
                    break
        
        # 5. HTF LEVEL PROXIMITY
        near_htf_level = False
        if self.use_proximity and self._htf_levels:
            min_distance = min(abs(price - level) / meta.tick_size for level in self._htf_levels)
            if min_distance <= 5.0:
                near_htf_level = True
                trade_reasons.append(f"🎯 Near HTF level: {min_distance:.1f} ticks")
        
        # 6. HTF BIAS ALIGNMENT
        htf_aligned = False
        if self.use_htf_bias and self._htf_bias:
            htf_aligned = True
            trade_reasons.append(f"📊 HTF Bias: {self._htf_bias}")
        
        # BUILD CONFLUENCE CONTEXT
        confluence_ctx = {
            "sweep": sweep_detected,
            "mss": mss_result,
            "fvg_active": fvg_active,
            "fvg_quality": fvg_quality,
            "order_block": nearby_ob,
            "near_level": near_htf_level,
            "htf_aligned": htf_aligned,
            "session_optimal": session_optimal
        }
        
        strategy_settings = {
            "use_sweep": self.use_sweep,
            "use_mss": self.use_mss,
            "use_fvg": self.use_fvg,
            "use_order_blocks": self.use_order_blocks,
            "use_htf_bias": self.use_htf_bias,
            "use_proximity": self.use_proximity,
            "use_session_filter": self.use_session_filter
        }
        
        # CALCULATE CONFLUENCE SCORE
        confidence, breakdown = calculate_confluence_score(confluence_ctx, strategy_settings)
        
        # SIGNAL GENERATION WITH ENHANCED LOGIC
        signal = None
        
        if confidence >= self.min_confidence_to_trade:
            # Primary signal from sweep
            if sweep_detected:
                if (sweep_detected["type"] == "buy" and guardrails.get("allow_long", True)):
                    signal = "long"
                    trade_reasons.append("✅ LONG: Liquidity sweep + confluence")
                elif (sweep_detected["type"] == "sell" and guardrails.get("allow_short", True)):
                    signal = "short"
                    trade_reasons.append("✅ SHORT: Liquidity sweep + confluence")
            
            # Secondary signal from market structure
            elif mss_result and confidence >= 0.6:
                mss_label = mss_result["label"]
                if mss_label == "bullish_bos" and guardrails.get("allow_long", True):
                    signal = "long"
                    trade_reasons.append("✅ LONG: Bullish BOS + confluence")
                elif mss_label == "bearish_bos" and guardrails.get("allow_short", True):
                    signal = "short"
                    trade_reasons.append("✅ SHORT: Bearish BOS + confluence")
            
            # Tertiary signal from FVG + OB confluence
            elif fvg_active and nearby_ob and confidence >= 0.65:
                if (nearby_ob["type"] == "bullish" and guardrails.get("allow_long", True)):
                    signal = "long"
                    trade_reasons.append("✅ LONG: FVG + Order Block confluence")
                elif (nearby_ob["type"] == "bearish" and guardrails.get("allow_short", True)):
                    signal = "short"
                    trade_reasons.append("✅ SHORT: FVG + Order Block confluence")
        
        # POSITION SIZING AND RISK MANAGEMENT
        suggested = {}
        if signal:
            stop, target = get_dynamic_stops_targets(price, signal, meta, avg_volatility, confidence)
            
            # Dynamic position sizing
            risk_dollars = float(guardrails.get("dollar_risk_per_trade", self.max_risk_per_trade))
            max_contracts = int(guardrails.get("max_contracts", 1))
            
            if self.use_dynamic_sizing and risk_dollars > 0:
                base_qty = qty_for_dollar_risk(risk_dollars, price, stop, meta)
                
                # Scale position based on confidence
                if confidence >= 0.8:
                    qty = min(max_contracts, max(base_qty, int(max_contracts * 0.8)))
                    trade_reasons.append(f"🚀 HIGH CONVICTION: {qty} contracts")
                elif confidence >= 0.65:
                    qty = min(max_contracts, max(base_qty, int(max_contracts * 0.6)))
                    trade_reasons.append(f"📈 MEDIUM CONVICTION: {qty} contracts")
                else:
                    qty = base_qty
                    trade_reasons.append(f"📊 STANDARD SIZE: {qty} contracts")
            else:
                qty = max_contracts
            
            qty = max(1, min(qty, max_contracts))
            
            suggested = {
                "entry": price,
                "stop": stop,
                "target": target,
                "qty": qty,
                "risk_reward": abs(target - price) / abs(stop - price)
            }
        
        # COMPILE RESULTS
        return {
            "signal": signal,
            "confidence": float(confidence),
            "breakdown": {k: float(v) for k, v in breakdown.items()},
            "suggested": suggested,
            "trade_reasons": trade_reasons,
            "analysis": {
                "market_structure": mss_result,
                "sweep_detected": sweep_detected,
                "fvg_active": fvg_active,
                "order_blocks_nearby": nearby_ob is not None,
                "htf_proximity": near_htf_level,
                "session_optimal": session_optimal,
                "volatility": float(avg_volatility),
                "confluence_count": len([v for v in breakdown.values() if v > 0])
            }
        }


def build(**kwargs) -> EnhancedSMCStrategy:
    """Factory function for strategy creation"""
    strategy = EnhancedSMCStrategy()
    for key, value in kwargs.items():
        if hasattr(strategy, key):
            setattr(strategy, key, value)
    return strategy
