# -*- coding: utf-8 -*-
from __future__ import annotations

from dataclasses import dataclass
from typing import Optional

@dataclass
class ContractMeta:
    """
    Per-contract constants. Extend as needed.
    """
    tick_size: float = 0.25
    tick_value: float = 5.0  # dollars per tick per contract

@dataclass
class RiskLimits:
    """
    Static configuration for risk controls.
    All dollar values are positive absolute amounts in account currency.
    """
    daily_loss_limit: float                 # DLL: daily stop line for the account
    max_loss_limit_floor: Optional[float]   # MLL trailing floor balance. If None, ignore.
    personal_trailing_daily_loss: Optional[float] = None  # optional personal trailing DLL

@dataclass
class RiskState:
    """
    Dynamic, per-day state. Reset at session start.
    """
    realized_today: float = 0.0
    dll_locked: bool = False
    mll_locked: bool = False
    notes: list[str] = None

    def __post_init__(self):
        self.notes = [] if self.notes is None else self.notes

def qty_for_dollar_risk(dollars: float, entry: float, stop: float, meta: ContractMeta) -> int:
    """
    Position size as floor(dollars / per-contract-risk).
    """
    if entry is None or stop is None:
        return 0
    ticks = abs(entry - stop) / max(meta.tick_size, 1e-9)
    if ticks <= 0:
        return 0
    per_contract = ticks * meta.tick_value
    if per_contract <= 0:
        return 0
    return max(0, int(dollars // per_contract))

def per_contract_risk(entry: float, stop: float, meta: ContractMeta) -> float:
    ticks = abs(entry - stop) / max(meta.tick_size, 1e-9)
    return ticks * meta.tick_value

def dollars_for_move(entry: float, exit_: float, meta: ContractMeta, qty: int, side: str) -> float:
    """
    Signed P&L in dollars for a move from entry to exit at given qty.
    """
    ticks = abs(exit_ - entry) / max(meta.tick_size, 1e-9)
    gross = ticks * meta.tick_value * qty
    if side == "long":
        return gross if exit_ >= entry else -gross
    if side == "short":
        return gross if exit_ <= entry else -gross
    return 0.0

def cushion_if_stop_hits(entry: float, stop: float, meta: ContractMeta,
                         qty: int, dll_remaining: float, mll_remaining: Optional[float]) -> float:
    planned = per_contract_risk(entry, stop, meta) * qty
    cushions = [dll_remaining - planned]
    if mll_remaining is not None:
        cushions.append(mll_remaining - planned)
    return min(cushions)

def clamp_qty_by_cushion(entry: float, stop: float, meta: ContractMeta,
                         qty: int, dll_remaining: float, mll_remaining: Optional[float]) -> int:
    """
    Reduces size so that, if the stop is hit, cushions remain non-negative.
    """
    if qty <= 0:
        return 0
    per_ct = per_contract_risk(entry, stop, meta)
    limits = []
    if per_ct > 0:
        limits.append(int(dll_remaining // per_ct))
        if mll_remaining is not None:
            limits.append(int(mll_remaining // per_ct))
    hard_cap = min(limits) if limits else 0
    return max(0, min(qty, hard_cap))

def should_lock(risk: RiskState, limits: RiskLimits,
                account_balance: Optional[float] = None,
                mll_floor: Optional[float] = None) -> RiskState:
    """
    Returns updated RiskState with lock flags flipped if a hard limit is breached.
    - DLL: lock for the remainder of the day
    - MLL: lock immediately if balance <= floor
    """
    if limits.daily_loss_limit is not None and risk.realized_today <= -abs(limits.daily_loss_limit):
        risk.dll_locked = True
        risk.notes.append("Daily Loss Limit breached. Trading locked for rest of session.")
    if mll_floor is not None and account_balance is not None and account_balance <= mll_floor:
        risk.mll_locked = True
        risk.notes.append("Maximum Loss Limit floor reached. Trading locked.")
    return risk

def remaining_dll(limits: RiskLimits, risk: RiskState) -> float:
    """
    Remaining daily loss cushion in dollars (>= 0).
    """
    return max(0.0, abs(limits.daily_loss_limit) + risk.realized_today)

def remaining_mll(account_balance: Optional[float],
                  mll_floor: Optional[float]) -> Optional[float]:
    if account_balance is None or mll_floor is None:
        return None
    return max(0.0, float(account_balance - mll_floor))
