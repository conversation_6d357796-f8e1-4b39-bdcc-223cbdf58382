# broker_adapter.py
import time
import random
import threading
from typing import Optional, Dict, List, Any
from datetime import datetime, timezone, timedelta, date
import httpx
from httpx import HTTPStatusError
import pandas as pd
import logging
import os
from dotenv import load_dotenv
from settings import settings

load_dotenv()

logger = logging.getLogger(__name__)

class _RateLimiter:
    def __init__(self, rate_per_sec: float = 3.0, burst: int = 6):
        self.rate = float(rate_per_sec)
        self.capacity = int(burst)
        self.tokens = float(burst)
        self.updated = time.monotonic()
        self.lock = threading.Lock()

    def wait(self):
        with self.lock:
            now = time.monotonic()
            # Refill tokens
            self.tokens = min(self.capacity, self.tokens + (now - self.updated) * self.rate)
            self.updated = now
            if self.tokens >= 1.0:
                self.tokens -= 1.0
                return
            # Need to wait
            to_sleep = (1.0 - self.tokens) / max(self.rate, 1e-9)
        time.sleep(to_sleep)
        self.wait()

class BrokerAdapter:
    """
    Synchronous ProjectX / TopstepX adapter using JSON login.
    """
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None):
        self.base_root = self._normalize_base(base_url or os.getenv("PROJECTX_BASE_URL"))
        self._token = token
        self._client = httpx.Client(
            timeout=25,
            limits=httpx.Limits(max_connections=4, max_keepalive_connections=4, keepalive_expiry=30),
            http2=True
        )
        self._limiter = _RateLimiter(rate_per_sec=3.0, burst=6)

    def _normalize_base(self, url: str | None) -> str:
        if not url:
            return "https://api.topstepx.com"
        u = url.strip().rstrip("/")
        if u.lower().endswith("/api"):
            u = u[:-4]
        return u
        
    def _api(self, path: str) -> str:
        return f"{self.base_root}/api{path if path.startswith('/') else '/' + path}"

    def _auth_headers(self, extra: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        token = self.get_token()
        h = {"accept": "application/json", "authorization": f"Bearer {token}"}
        if extra:
            h.update(extra)
        return h

    def get_token(self) -> str:
        if not self._token:
            raise RuntimeError("No token present. Call login_with_api_key() or set a token.")
        return self._token

    def login_with_api_key(self) -> None:
        if self._token:
            return
        
        username = settings.username
        api_key = settings.api_key
        
        if not username or not api_key:
            raise RuntimeError("Missing PROJECTX_USERNAME or PROJECTX_API_KEY in settings")
        
        url = self._api("/Auth/loginKey")
        payload = {
            "userName": username,
            "apiKey": api_key
        }
        
        print(f"Attempting login to: {url}")
        print(f"Username: {username}")
        print(f"API Key: {api_key[:10]}...")
        
        try:
            r = self._client.post(
                url, 
                headers={"accept": "application/json", "content-type": "application/json"},
                json=payload
            )
            print(f"Response status: {r.status_code}")
            print(f"Response headers: {dict(r.headers)}")
            print(f"Response body: {r.text}")
            
            r.raise_for_status()
            data = r.json()
            token = data.get("token") or data.get("jwt")
            if not token:
                raise RuntimeError(f"loginKey returned no token: {data}")
            self._token = token
            print("Login successful!")
            
        except Exception as e:
            print(f"Login failed: {e}")
            raise

    def _post(self, path: str, json: dict, extra_headers: Optional[Dict[str, str]] = None, max_retries: int = 5):
        url = self._api(path)
        headers = self._auth_headers({"content-type": "application/json"} | (extra_headers or {}))
        attempt = 0
        while True:
            self._limiter.wait()
            try:
                r = self._client.post(url, headers=headers, json=json)
                r.raise_for_status()
                return r
            except HTTPStatusError as e:
                status = e.response.status_code
                if status == 429:
                    ra = e.response.headers.get("Retry-After")
                    if ra:
                        try:
                            delay = float(ra)
                        except ValueError:
                            delay = 5.0
                    else:
                        delay = min(30.0, (2 ** attempt) + random.uniform(0, 1))
                    logger.warning(f"Rate limited, waiting {delay:.1f}s")
                    time.sleep(delay)
                    attempt += 1
                    if attempt <= max_retries:
                        continue
                if 500 <= status < 600 and attempt < max_retries:
                    delay = min(30.0, (2 ** attempt) + random.uniform(0, 1))
                    time.sleep(delay)
                    attempt += 1
                    continue
                raise

    def list_accounts(self, only_active: bool = True) -> List[Dict[str, Any]]:
        r = self._post("/Account/search", {"onlyActiveAccounts": bool(only_active)})
        data = r.json()
        return data.get("accounts", []) if isinstance(data, dict) else data

    def search_contracts(self, search_text: str = "", live: bool = False) -> List[Dict[str, Any]]:
        r = self._post("/Contract/search", {"live": bool(live), "searchText": search_text or ""})
        data = r.json()
        return data.get("contracts", []) if isinstance(data, dict) else data

    def retrieve_bars(
        self,
        contract_id: str,
        start_iso: str,
        end_iso: str,
        unit: int = 2,
        unit_number: int = 1,
        live: bool = False,
        include_partial: bool = True,
        limit: int = 20000,
    ) -> List[Dict[str, Any]]:
        payload = {
            "contractId": contract_id,
            "live": bool(live),
            "startTime": start_iso,
            "endTime": end_iso,
            "unit": int(unit),
            "unitNumber": int(unit_number),
            "limit": int(limit),
            "includePartialBar": bool(include_partial),
        }
        r = self._post("/History/retrieveBars", payload)
        data = r.json()
        bars = data.get("bars", []) if isinstance(data, dict) else data
        return bars if isinstance(bars, list) else []

    def last_close_price(self, contract_id: str, live: bool = False) -> Optional[float]:
        """Get the most recent price with better error handling"""
        try:
            end = datetime.now(timezone.utc)
            start = end - timedelta(minutes=10)  # Look back 10 minutes instead of 5
            bars = self.retrieve_bars(
                contract_id, 
                start.isoformat(), 
                end.isoformat(),
                unit=2, 
                unit_number=1, 
                live=live, 
                include_partial=True, 
                limit=100
            )
            if bars:
                # Try to get the most recent close price
                return float(bars[-1].get("c", bars[-1].get("close", 0)))
        except Exception as e:
            print(f"Error fetching last price: {e}")
        return None

    def place_market_order(self, account_id: int, contract_id: str, side: int, qty: int,
                           custom_tag: Optional[str] = None, client_order_id: Optional[str] = None) -> Dict[str, Any]:
        payload = {"accountId": account_id, "contractId": contract_id, "side": side, "type": "MKT",
                   "qty": qty, "customTag": custom_tag, "clientOrderId": client_order_id}
        r = self._post("/Order/place", payload)
        return r.json()

    def place_limit_order(self, account_id: int, contract_id: str, side: int, qty: int, limit_price: float,
                          custom_tag: Optional[str] = None, client_order_id: Optional[str] = None) -> Dict[str, Any]:
        payload = {"accountId": account_id, "contractId": contract_id, "side": side, "type": "LMT",
                   "qty": qty, "limitPrice": float(limit_price), "customTag": custom_tag,
                   "clientOrderId": client_order_id}
        r = self._post("/Order/place", payload)
        return r.json()

    def place_stop_order(self, account_id: int, contract_id: str, side: int, qty: int, stop_price: float,
                         linked_order_id: Optional[str] = None, custom_tag: Optional[str] = None) -> Dict[str, Any]:
        payload = {"accountId": account_id, "contractId": contract_id, "side": side, "type": "STP",
                   "qty": qty, "stopPrice": float(stop_price), "linkedOrderId": linked_order_id,
                   "customTag": custom_tag}
        r = self._post("/Order/place", payload)
        return r.json()

    def place_target_order(self, account_id: int, contract_id: str, side: int, qty: int, limit_price: float,
                           linked_order_id: Optional[str] = None, custom_tag: Optional[str] = None) -> Dict[str, Any]:
        payload = {"accountId": account_id, "contractId": contract_id, "side": side, "type": "TGT",
                   "qty": qty, "limitPrice": float(limit_price), "linkedOrderId": linked_order_id,
                   "customTag": custom_tag}
        r = self._post("/Order/place", payload)
        return r.json()

    # idempotent bracket (safe retries)
    def _coid(self, tag: str, side: str, entry: float | None, qty: int) -> str:
        e = f"{int(entry*100)}" if entry else "mkt"
        return f"{tag}-{side}-{e}-{qty}"

    def place_bracket_idem(self, account_id: int, contract_id: str, side: str, qty: int,
                          entry_type: str = "MKT", entry_price: Optional[float] = None,
                          stop_price: Optional[float] = None, target_price: Optional[float] = None,
                          tag: str = "bracket") -> Dict[str, Any]:
        """Place bracket order using ProjectX API structure"""
        try:
            
            # CRITICAL FIX: Use ProjectX numeric enums for side and type
            # Map side: 0 = Buy/Long, 1 = Sell/Short
            side_enum = 0 if side.lower() in ["long", "buy"] else 1

            # Map order type to numeric enum (verify these with your ProjectX tenant)
            # Common mappings: MARKET=2, LIMIT=1, STOP=3 (check your docs!)
            type_mapping = {
                "MKT": 2,    # Market order
                "LMT": 1,    # Limit order
                "STP": 3     # Stop order
            }
            type_enum = type_mapping.get(entry_type.upper(), 2)

            # 1) Place parent order using ProjectX schema
            parent_payload = {
                "accountId": account_id,
                "contractId": contract_id,
                "side": side_enum,
                "type": type_enum,
                "size": int(qty),
                "tag": tag
            }

            if entry_type.upper() == "LMT" and entry_price is not None:
                parent_payload["limitPrice"] = float(entry_price)

            logger.info(f"Placing parent order: {parent_payload}")
            r = self._post("/Order/place", parent_payload)

            parent_result = r.json()
            parent_id = parent_result.get("orderId") or parent_result.get("id")
            logger.info(f"Parent order placed: {parent_result}")

            # 2) Place stop loss child order if specified
            if stop_price is not None and parent_id:
                stop_payload = {
                    "accountId": account_id,
                    "contractId": contract_id,
                    "side": 1 - side_enum,      # Opposite side for exit
                    "type": type_mapping["STP"], # Stop order
                    "size": int(qty),
                    "stopPrice": float(stop_price),
                    "linkedOrderId": parent_id,
                    "tag": f"{tag}-sl"
                }

                logger.info(f"Placing stop loss: {stop_payload}")
                r2 = self._post("/Order/place", stop_payload)
                logger.info(f"Stop loss placed: {r2.json()}")

            # 3) Place take profit child order if specified
            if target_price is not None and parent_id:
                target_payload = {
                    "accountId": account_id,
                    "contractId": contract_id,
                    "side": 1 - side_enum,      # Opposite side for exit
                    "type": type_mapping["LMT"], # Limit order
                    "size": int(qty),
                    "limitPrice": float(target_price),
                    "linkedOrderId": parent_id,
                    "tag": f"{tag}-tp"
                }

                logger.info(f"Placing take profit: {target_payload}")
                r3 = self._post("/Order/place", target_payload)
                logger.info(f"Take profit placed: {r3.json()}")

            return {"parent": parent_result, "orderId": parent_id}
            
        except Exception as e:
            logger.error(f"Order placement failed: {e}")
            raise

    # order maintenance
    def cancel_order(self, account_id: int, order_id: str) -> Dict[str, Any]:
        r = self._post("/Order/cancel", {"accountId": account_id, "orderId": order_id})
        return r.json()

    def cancel_all_orders(self, account_id: int) -> Dict[str, Any]:
        r = self._post("/Order/cancelAll", {"accountId": account_id})
        return r.json()

    def search_open_orders(self, account_id: int) -> List[Dict[str, Any]]:
        """Get open orders using correct ProjectX API endpoint"""
        try:
            payload = {
                "accountId": account_id
            }

            r = self._post("/Order/searchOpen", payload)

            data = r.json()
            if isinstance(data, dict) and "orders" in data:
                return data["orders"]
            elif isinstance(data, list):
                return data
            return []

        except Exception as e:
            logger.warning(f"Open order search failed: {e}")
            return []

    def close_position(self, account_id: int, contract_id: str) -> Dict[str, Any]:
        r = self._post("/Position/close", {"accountId": account_id, "contractId": contract_id})
        return r.json()

    def close_all_positions(self, account_id: int) -> Dict[str, Any]:
        r = self._post("/Position/closeAll", {"accountId": account_id})
        return r.json()

    # pnl (optional)
    def realized_pnl_today(self, account_id: int) -> float:
        """Get today's realized P&L from account summary or trades"""
        try:
            logger.info(f"Calculating daily P&L for account {account_id}")
            summary = self.get_account_summary(account_id)

            # Try different possible field names for daily P&L
            pnl_fields = [
                "realizedPnlToday", "dailyRealizedPnl", "todayRealizedPnl",
                "realizedPnl", "dailyPnl", "todayPnl", "pnlToday",
                "dayPnl", "sessionPnl"
            ]

            for field in pnl_fields:
                if field in summary and summary[field] is not None:
                    pnl_value = float(summary[field])
                    logger.info(f"Found P&L field '{field}': {pnl_value}")
                    if pnl_value != 0.0:  # Only return non-zero values from account summary
                        return pnl_value

            logger.info("No P&L found in account summary, calculating from trades")
            # If no specific daily field, try to calculate from trades
            return self._calculate_daily_pnl(account_id)

        except Exception as e:
            logger.warning(f"Daily P&L calculation failed: {e}")
            return 0.0

    def get_comprehensive_pnl(self, account_id: int) -> dict:
        """Get comprehensive P&L breakdown including realized and unrealized"""
        try:
            summary = self.get_account_summary(account_id)
            realized_pnl = self.realized_pnl_today(account_id)

            # Get unrealized P&L from positions
            unrealized_pnl = 0.0
            try:
                positions = self.list_positions(account_id) or []
                for pos in positions:
                    if float(pos.get("qty", 0)) != 0:
                        unrealized_pnl += float(pos.get("unrealizedPnl", 0))
            except Exception as e:
                logger.warning(f"Could not calculate unrealized P&L: {e}")

            return {
                "realized_pnl": realized_pnl,
                "unrealized_pnl": unrealized_pnl,
                "total_pnl": realized_pnl + unrealized_pnl,
                "account_balance": summary.get("balance", 0),
                "account_fields": list(summary.keys()) if summary else []
            }

        except Exception as e:
            logger.warning(f"Comprehensive P&L calculation failed: {e}")
            return {
                "realized_pnl": 0.0,
                "unrealized_pnl": 0.0,
                "total_pnl": 0.0,
                "account_balance": 0.0,
                "account_fields": []
            }

    def get_account_summary(self, account_id: int) -> Dict[str, Any]:
        """Get account summary with P&L data - always fetch fresh data"""
        try:
            logger.info(f"Fetching fresh account summary for account {account_id}")
            url = self._api("/Account/search")
            headers = self._auth_headers({"content-type": "application/json"})

            payload = {"onlyActiveAccounts": True}

            r = self._client.post(url, headers=headers, json=payload)
            r.raise_for_status()

            data = r.json()
            logger.info(f"Account search response keys: {list(data.keys()) if isinstance(data, dict) else 'not dict'}")

            if isinstance(data, dict) and "accounts" in data:
                # Find the specific account
                for account in data["accounts"]:
                    if account.get("id") == account_id:
                        logger.info(f"Found account {account_id} with fields: {list(account.keys())}")
                        return account

            logger.warning(f"Account {account_id} not found in response")
            return {}

        except Exception as e:
            logger.warning(f"Account summary failed: {e}")
            return {}

    def _calculate_daily_pnl(self, account_id: int) -> float:
        """Calculate daily P&L from today's trades using TopStepX trading session times"""
        try:
            from datetime import date, datetime, timedelta
            from zoneinfo import ZoneInfo

            # TopStepX operates on futures hours - trading session starts Sunday 6 PM ET
            ET = ZoneInfo("America/New_York")
            now_et = datetime.now(ET)
            today = date.today()

            logger.info(f"Calculating P&L from trades for TopStepX session on {today}")

            # Determine the current trading session boundaries
            # For TopStepX futures, we want to capture all trades from the current active session
            # Since we're currently on Sunday evening and the market is open,
            # we should look at trades from the entire weekend period (Friday close to now)

            if now_et.weekday() == 6:  # Sunday
                if now_et.time() >= datetime.strptime("18:00", "%H:%M").time():
                    # Sunday after 6 PM - capture trades from Friday close to now
                    # This ensures we get the full weekend trading session
                    session_start = datetime.combine(today - timedelta(days=2), datetime.strptime("17:00", "%H:%M").time()).replace(tzinfo=ET)  # Friday 5 PM
                    session_end = now_et  # Current time
                else:
                    # Sunday before 6 PM - use previous session (Friday close to Sunday 6 PM)
                    session_start = datetime.combine(today - timedelta(days=2), datetime.strptime("17:00", "%H:%M").time()).replace(tzinfo=ET)
                    session_end = datetime.combine(today, datetime.strptime("18:00", "%H:%M").time()).replace(tzinfo=ET)
            elif now_et.weekday() == 5:  # Saturday
                # Saturday - capture from Friday close to now
                session_start = datetime.combine(today - timedelta(days=1), datetime.strptime("17:00", "%H:%M").time()).replace(tzinfo=ET)
                session_end = now_et
            else:  # Monday-Friday
                # Regular weekday - session started yesterday at 6 PM, ends today at 5 PM
                session_start = datetime.combine(today - timedelta(days=1), datetime.strptime("18:00", "%H:%M").time()).replace(tzinfo=ET)
                session_end = datetime.combine(today, datetime.strptime("17:00", "%H:%M").time()).replace(tzinfo=ET)

            logger.info(f"Trading session: {session_start} to {session_end}")

            url = self._api("/Trade/search")
            headers = self._auth_headers({"content-type": "application/json"})

            # Try different time formats and ranges
            # Start with a broader range to capture all recent trades
            yesterday = today - timedelta(days=1)
            day_before = today - timedelta(days=2)

            payload_options = [
                # Try session-based range first
                {
                    "accountId": account_id,
                    "startTimestamp": session_start.isoformat(),
                    "endTimestamp": session_end.isoformat()
                },
                # Try last 3 days to capture weekend trading
                {
                    "accountId": account_id,
                    "startDate": day_before.isoformat(),
                    "endDate": today.isoformat()
                },
                # Try today only
                {
                    "accountId": account_id,
                    "startDate": today.isoformat(),
                    "endDate": today.isoformat()
                },
                # Try yesterday to today
                {
                    "accountId": account_id,
                    "startDate": yesterday.isoformat(),
                    "endDate": today.isoformat()
                },
                # Simple date format
                {
                    "accountId": account_id,
                    "date": today.isoformat()
                }
            ]

            for i, payload in enumerate(payload_options):
                try:
                    logger.info(f"Trying trade search payload {i+1}: {payload}")
                    r = self._client.post(url, headers=headers, json=payload)
                    r.raise_for_status()

                    data = r.json()
                    trades = data if isinstance(data, list) else data.get("trades", data.get("data", []))

                    if trades:
                        logger.info(f"Found {len(trades)} trades with payload {i+1}")
                        # Debug: log first trade structure
                        if len(trades) > 0:
                            logger.info(f"Sample trade fields: {list(trades[0].keys())}")
                            logger.info(f"Sample trade data: {trades[0]}")

                        total_pnl = 0.0
                        for j, trade in enumerate(trades):
                            # Use profitAndLoss field for actual P&L, not fees
                            trade_pnl = 0.0
                            found_field = None

                            # First try profitAndLoss (the main P&L field)
                            if "profitAndLoss" in trade and trade["profitAndLoss"] is not None:
                                trade_pnl = float(trade["profitAndLoss"])
                                found_field = "profitAndLoss"
                            # Fallback to other P&L fields
                            else:
                                pnl_fields = ["realizedPnl", "pnl", "profit", "netPnl", "grossPnl"]
                                for field in pnl_fields:
                                    if field in trade and trade[field] is not None:
                                        trade_pnl = float(trade[field])
                                        found_field = field
                                        break

                            # Don't include fees in P&L calculation - they're separate
                            total_pnl += trade_pnl
                            if j < 10:  # Log first 10 trades for debugging
                                logger.info(f"Trade {j+1}: {found_field}={trade_pnl}, Running total: {total_pnl}")
                                if j < 3:  # Show full trade data for first 3 trades
                                    logger.info(f"  Full trade data: {trade}")

                        logger.info(f"Total daily P&L from {len(trades)} trades: {total_pnl}")
                        return total_pnl
                    else:
                        logger.info(f"No trades found with payload {i+1}")

                except Exception as payload_error:
                    logger.warning(f"Payload {i+1} failed: {payload_error}")
                    continue

            logger.info("No trades found with any payload format")
            return 0.0

        except Exception as e:
            logger.warning(f"Trade-based P&L calculation failed: {e}")
            return 0.0

    def check_available_endpoints(self) -> List[str]:
        """Check what endpoints are available for debugging"""
        endpoints_to_check = [
            "/signalr/negotiate",
            "/hub/negotiate", 
            "/api/hub/negotiate",
            "/realtime/negotiate",
            "/ws",
            "/websocket"
        ]
        
        available = []
        for endpoint in endpoints_to_check:
            try:
                url = self._api(endpoint)
                r = self._client.get(url, headers=self._auth_headers(), timeout=5)
                if r.status_code != 404:
                    available.append(f"{endpoint} -> {r.status_code}")
            except:
                pass
        
        return available

    def get_account_details(self, account_id: int) -> Dict[str, Any]:
        """Get detailed account information using the correct endpoint"""
        try:
            # Use the same endpoint as get_account_summary since /Account/details doesn't exist
            return self.get_account_summary(account_id)
        except Exception as e:
            logger.warning(f"Account details failed: {e}")
            return {}

    def list_positions(self, account_id: int) -> List[Dict[str, Any]]:
        """Get current positions using correct ProjectX API"""
        try:
            url = self._api("/Position/searchOpen")
            headers = self._auth_headers({"content-type": "application/json"})
            
            # Use the correct payload structure from ProjectX docs
            payload = {
                "accountId": account_id
            }
            
            r = self._client.post(url, headers=headers, json=payload)
            r.raise_for_status()
            
            data = r.json()
            # Handle different response structures
            if isinstance(data, list):
                return data
            elif isinstance(data, dict):
                return data.get("positions", data.get("data", []))
            return []
            
        except Exception as e:
            logger.warning(f"Position search failed: {e}")
            return []

    def list_orders(self, account_id: int) -> List[Dict[str, Any]]:
        """Get orders using correct ProjectX API"""
        try:
            url = self._api("/Order/search")
            headers = self._auth_headers({"content-type": "application/json"})

            # Use correct payload structure with required timestamps
            from datetime import datetime, timedelta, timezone

            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=7)  # Last 7 days

            # Format timestamps according to API documentation
            payload = {
                "accountId": account_id,
                "startTimestamp": start_time.isoformat(),
                "endTimestamp": end_time.isoformat()
            }

            r = self._client.post(url, headers=headers, json=payload)
            r.raise_for_status()

            data = r.json()
            if isinstance(data, dict) and "orders" in data:
                return data["orders"]
            elif isinstance(data, list):
                return data
            return []

        except Exception as e:
            logger.warning(f"Order search failed: {e}")
            return []

    def get_historical_trades(self, account_id: int, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """Get historical trades for date range"""
        url = self._api("/Trade/search")
        r = self._client.post(url, headers=self._auth_headers({"content-type": "application/json"}),
                              json={
                                  "accountId": account_id,
                                  "startTimestamp": start_date.isoformat() + "T00:00:00Z",
                                  "endTimestamp": end_date.isoformat() + "T23:59:59Z"
                              })
        r.raise_for_status()
        data = r.json()
        return data if isinstance(data, list) else data.get("trades", [])

    def get_minute_bars(self, contract_id: str, start_dt: datetime, end_dt: datetime, live: bool = False) -> pd.DataFrame:
        """
        Get minute bars and return as DataFrame with standardized columns
        """
        try:
            bars = self.retrieve_bars(
                contract_id=contract_id,
                start_iso=start_dt.isoformat(),
                end_iso=end_dt.isoformat(),
                unit=2,  # Minutes
                unit_number=1,  # 1-minute bars
                live=live,
                include_partial=True,
                limit=20000
            )
            
            if not bars:
                return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])
            
            # Convert to DataFrame and standardize columns
            df = pd.DataFrame(bars)
            
            # Map API response columns to expected columns - CRITICAL FIX: Include ProjectX 't' key
            column_mapping = {
                't': 'time_et',           # ProjectX bars key
                'time': 'time_et',
                'timestamp': 'time_et',
                'open': 'o',
                'high': 'h',
                'low': 'l',
                'close': 'c',
                'volume': 'v'
            }
            
            # Rename columns if they exist
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df = df.rename(columns={old_col: new_col})
            
            # Ensure we have the required columns
            required_cols = ["time_et", "o", "h", "l", "c", "v"]
            for col in required_cols:
                if col not in df.columns:
                    if col == "v":
                        df[col] = 0
                    elif col == "time_et":
                        df[col] = pd.date_range(start=start_dt, periods=len(df), freq='1min')
                    else:
                        df[col] = 0.0
            
            # Convert time to datetime and localize to ET
            if 'time_et' in df.columns:
                df['time_et'] = pd.to_datetime(df['time_et'])
                if df['time_et'].dt.tz is None:
                    df['time_et'] = df['time_et'].dt.tz_localize('UTC').dt.tz_convert('America/New_York')
            
            return df[required_cols]
            
        except Exception as e:
            logger.error(f"Error in get_minute_bars: {e}")
            return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])

    def debug_api_endpoints(self, account_id: int) -> Dict[str, Any]:
        """Debug API endpoints to find correct ones"""
        results = {}

        # Generate proper timestamps for Order/search
        from datetime import datetime, timedelta, timezone
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=7)

        # Test various endpoint patterns (only testing documented endpoints)
        endpoints_to_test = [
            # Position endpoints (correct endpoint)
            ("/Position/searchOpen", "POST", {"accountId": account_id}),

            # Order endpoints (both working correctly)
            ("/Order/search", "POST", {"accountId": account_id, "startTimestamp": start_time.isoformat(), "endTimestamp": end_time.isoformat()}),
            ("/Order/searchOpen", "POST", {"accountId": account_id}),

            # Trade endpoints
            ("/Trade/search", "POST", {"accountId": account_id, "startDate": "2024-01-01", "endDate": "2024-12-31"}),

            # Account endpoints (only documented one)
            ("/Account/search", "POST", {"onlyActiveAccounts": True}),

            # Contract endpoints
            ("/Contract/search", "POST", {"searchText": "NQ", "live": True}),
            ("/Contract/searchById", "POST", {"contractId": "CON.F.US.ENQ.U25"}),
        ]
        
        for endpoint, method, params in endpoints_to_test:
            try:
                url = self._api(endpoint)
                headers = self._auth_headers({"content-type": "application/json"})
                
                if method == "GET":
                    if params:
                        r = self._client.get(url, headers=headers, params=params)
                    else:
                        r = self._client.get(url, headers=headers)
                else:  # POST
                    r = self._client.post(url, headers=headers, json=params)
                
                results[f"{method} {endpoint}"] = {
                    "status": r.status_code,
                    "success": r.status_code < 400,
                    "response_size": len(r.content) if r.content else 0
                }
                
                # If successful, try to get a sample of the response
                if r.status_code < 400:
                    try:
                        data = r.json()
                        if isinstance(data, list):
                            results[f"{method} {endpoint}"]["sample"] = f"List with {len(data)} items"
                        elif isinstance(data, dict):
                            results[f"{method} {endpoint}"]["sample"] = f"Dict with keys: {list(data.keys())[:5]}"
                        else:
                            results[f"{method} {endpoint}"]["sample"] = str(data)[:100]
                    except:
                        results[f"{method} {endpoint}"]["sample"] = "Non-JSON response"
                        
            except Exception as e:
                results[f"{method} {endpoint}"] = {
                    "status": "ERROR",
                    "error": str(e)
                }
        
        return results

    def debug_account_structure(self) -> Dict[str, Any]:
        """Debug account structure to understand API"""
        try:
            # Get accounts with full response
            url = self._api("/Account/search")
            headers = self._auth_headers({"content-type": "application/json"})
            r = self._client.post(url, headers=headers, json={"onlyActiveAccounts": True})
            r.raise_for_status()
            
            full_response = r.json()
            
            return {
                "raw_response": full_response,
                "response_type": type(full_response).__name__,
                "keys": list(full_response.keys()) if isinstance(full_response, dict) else "Not a dict",
                "first_account": full_response.get("accounts", [{}])[0] if isinstance(full_response, dict) else full_response[0] if isinstance(full_response, list) and full_response else {}
            }
        except Exception as e:
            return {"error": str(e)}

    def simple_endpoint_test(self) -> Dict[str, Any]:
        """Simple test of basic endpoints that should work"""
        results = {}

        # Test only endpoints we know work (no more 404 GET endpoints)
        working_endpoints = [
            ("/Account/search", "POST", {"onlyActiveAccounts": True}),
            ("/Contract/search", "POST", {"live": False, "searchText": ""}),
            ("/Auth/loginKey", "POST", {"userName": "test", "apiKey": "test"}),
        ]

        for endpoint, method, payload in working_endpoints:
            try:
                url = self._api(endpoint)
                headers = self._auth_headers({"content-type": "application/json"})
                
                if method == "GET":
                    r = self._client.get(url, headers=headers, timeout=3)
                else:
                    r = self._client.post(url, headers=headers, json=payload, timeout=3)
                
                results[f"{method} {endpoint}"] = {
                    "status": r.status_code,
                    "success": r.status_code < 400,
                    "size": len(r.content) if r.content else 0
                }
                
            except Exception as e:
                results[f"{method} {endpoint}"] = {
                    "status": "ERROR", 
                    "error": str(e)
                }
        
        return results

    def test_projectx_endpoints(self, account_id: int) -> Dict[str, Any]:
        """Test ProjectX API endpoints with correct payloads"""
        results = {}
        
        # Test endpoints based on ProjectX documentation
        from datetime import datetime, timedelta, timezone

        # Generate proper timestamps for Order/search
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=7)

        test_cases = [
            # Account endpoints (only /Account/search exists)
            ("/Account/search", "POST", {"onlyActiveAccounts": True}),

            # Position endpoints (correct endpoint is /Position/searchOpen)
            ("/Position/searchOpen", "POST", {"accountId": account_id}),

            # Order endpoints (both working correctly)
            ("/Order/search", "POST", {"accountId": account_id, "startTimestamp": start_time.isoformat(), "endTimestamp": end_time.isoformat()}),
            ("/Order/searchOpen", "POST", {"accountId": account_id}),

            # Trade endpoints
            ("/Trade/search", "POST", {"accountId": account_id, "startDate": "2024-01-01", "endDate": "2024-12-31"}),

            # Contract endpoints (correct endpoints from documentation)
            ("/Contract/search", "POST", {"searchText": "NQ", "live": True}),
            ("/Contract/searchById", "POST", {"contractId": "CON.F.US.ENQ.U25"}),

            # History endpoints (for market data)
            ("/History/retrieveBars", "POST", {
                "contractId": "CON.F.US.ENQ.U25",
                "live": False,
                "startTime": "2025-07-27T00:00:00Z",
                "endTime": "2025-07-28T00:00:00Z",
                "unit": 2,  # 2 = Minute
                "unitNumber": 1,
                "limit": 100,
                "includePartialBar": False
            }),
        ]
        
        for endpoint, method, payload in test_cases:
            try:
                url = self._api(endpoint)
                headers = self._auth_headers({"content-type": "application/json"})
                
                r = self._client.post(url, headers=headers, json=payload, timeout=10)
                
                results[f"{method} {endpoint}"] = {
                    "status": r.status_code,
                    "success": r.status_code < 400,
                    "response_size": len(r.content) if r.content else 0
                }
                
                if r.status_code < 400:
                    try:
                        data = r.json()
                        if isinstance(data, dict):
                            results[f"{method} {endpoint}"]["keys"] = list(data.keys())[:10]
                        elif isinstance(data, list):
                            results[f"{method} {endpoint}"]["count"] = len(data)
                    except:
                        results[f"{method} {endpoint}"]["note"] = "Non-JSON response"
                        
            except Exception as e:
                results[f"{method} {endpoint}"] = {
                    "status": "ERROR",
                    "error": str(e)
                }
        
        return results
