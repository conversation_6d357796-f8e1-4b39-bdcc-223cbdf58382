# api_client.py
"""
Optimized API client with request coalescing, caching, circuit breaker, and adaptive polling.
Implements all the performance optimizations recommended for reducing API load.
"""

import asyncio
import hashlib
import logging
import random
import time
import threading
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, List, Any, Callable, Hashable, Union
from dataclasses import dataclass, field
from enum import Enum
import httpx
from httpx import HTTPStatusError

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit breaker tripped
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CacheEntry:
    """Cache entry with TTL support"""
    data: Any
    expires_at: float
    
    def is_expired(self) -> bool:
        return time.time() > self.expires_at


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5  # Consecutive failures to trip
    recovery_timeout: float = 60.0  # Seconds before half-open
    success_threshold: int = 2  # Successes needed to close from half-open


class CircuitBreaker:
    """Circuit breaker implementation for API resilience"""
    
    def __init__(self, config: CircuitBreakerConfig = None):
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function through circuit breaker"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if time.time() - self.last_failure_time > self.config.recovery_timeout:
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info("Circuit breaker moving to HALF_OPEN")
                else:
                    raise Exception("Circuit breaker is OPEN - service unavailable")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _on_success(self):
        """Handle successful call"""
        with self.lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    logger.info("Circuit breaker CLOSED - service recovered")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed call"""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning("Circuit breaker OPEN - service still failing")
            elif (self.state == CircuitState.CLOSED and 
                  self.failure_count >= self.config.failure_threshold):
                self.state = CircuitState.OPEN
                logger.warning(f"Circuit breaker OPEN - {self.failure_count} consecutive failures")


class RequestCoalescer:
    """Coalesces identical in-flight requests to prevent duplicate API calls"""
    
    def __init__(self):
        self._inflight: Dict[Hashable, asyncio.Task] = {}
        self._lock = asyncio.Lock()
    
    async def coalesce(self, key: Hashable, coro_factory: Callable):
        """Coalesce requests with the same key"""
        async with self._lock:
            if key in self._inflight:
                logger.debug(f"Coalescing request for key: {key}")
                return await self._inflight[key]
            
            task = asyncio.create_task(coro_factory())
            self._inflight[key] = task
            
            try:
                result = await task
                return result
            finally:
                async with self._lock:
                    self._inflight.pop(key, None)


class AdaptivePoller:
    """Adaptive polling that adjusts intervals based on data availability"""
    
    def __init__(self, initial_interval: float = 3.0):
        self.initial_interval = initial_interval
        self.current_interval = initial_interval
        self.empty_streak = 0
        self.last_data_time = time.time()
    
    def update(self, has_new_data: bool) -> float:
        """Update polling interval based on data availability"""
        if has_new_data:
            self.empty_streak = 0
            self.current_interval = self.initial_interval
            self.last_data_time = time.time()
            logger.debug("Data received - polling at normal rate")
        else:
            self.empty_streak += 1
            if self.empty_streak == 3:
                self.current_interval = 15.0
                logger.debug("3 empty responses - backing off to 15s")
            elif self.empty_streak >= 6:
                self.current_interval = 45.0
                logger.debug("6+ empty responses - backing off to 45s")
        
        return self.current_interval


class OptimizedAPIClient:
    """
    Optimized API client with caching, request coalescing, circuit breaker,
    and adaptive polling to minimize API load and handle rate limits gracefully.
    """
    
    def __init__(self, base_url: str, token: str = None):
        self.base_url = base_url.rstrip('/')
        self.token = token
        
        # HTTP client with optimized settings
        self.client = httpx.AsyncClient(
            http2=True,
            limits=httpx.Limits(
                max_connections=10,
                max_keepalive_connections=10,
                keepalive_expiry=30
            ),
            timeout=httpx.Timeout(10.0, connect=5.0)
        )
        
        # Components
        self.circuit_breaker = CircuitBreaker()
        self.coalescer = RequestCoalescer()
        self.cache: Dict[str, CacheEntry] = {}
        self.cache_lock = threading.Lock()
        
        # Adaptive pollers for different data types
        self.live_bars_poller = AdaptivePoller(initial_interval=3.0)
        self.account_poller = AdaptivePoller(initial_interval=10.0)
        
    def _cache_key(self, method: str, url: str, data: Dict = None) -> str:
        """Generate cache key for request"""
        key_data = f"{method}:{url}"
        if data:
            # Sort keys for consistent hashing
            sorted_data = str(sorted(data.items()))
            key_data += f":{sorted_data}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached(self, cache_key: str) -> Optional[Any]:
        """Get cached response if not expired"""
        with self.cache_lock:
            entry = self.cache.get(cache_key)
            if entry and not entry.is_expired():
                logger.debug(f"Cache hit for key: {cache_key}")
                return entry.data
            elif entry:
                # Remove expired entry
                del self.cache[cache_key]
        return None
    
    def _set_cache(self, cache_key: str, data: Any, ttl_seconds: float):
        """Cache response with TTL"""
        with self.cache_lock:
            expires_at = time.time() + ttl_seconds
            self.cache[cache_key] = CacheEntry(data, expires_at)
            logger.debug(f"Cached response for {ttl_seconds}s: {cache_key}")
    
    async def _request_with_backoff(self, method: str, url: str, **kwargs) -> httpx.Response:
        """Make HTTP request with exponential backoff and jitter"""
        max_retries = 5
        base_delay = 0.5
        
        for attempt in range(1, max_retries + 1):
            try:
                response = await self.client.request(method, url, **kwargs)
                
                if response.status_code == 429:
                    # Respect Retry-After header
                    retry_after = response.headers.get("Retry-After")
                    if retry_after:
                        try:
                            delay = float(retry_after)
                        except ValueError:
                            # Could be HTTP-date format, use default
                            delay = base_delay * (2 ** attempt)
                    else:
                        # Full jitter exponential backoff
                        delay = random.uniform(0, base_delay * (2 ** attempt))
                    
                    logger.warning(f"Rate limited (429), waiting {delay:.1f}s (attempt {attempt})")
                    await asyncio.sleep(delay)
                    continue
                
                if response.status_code >= 500:
                    # Server error - exponential backoff with jitter
                    delay = random.uniform(0, base_delay * (2 ** attempt))
                    logger.warning(f"Server error {response.status_code}, waiting {delay:.1f}s (attempt {attempt})")
                    await asyncio.sleep(delay)
                    continue
                
                response.raise_for_status()
                return response
                
            except httpx.RequestError as e:
                if attempt == max_retries:
                    raise
                delay = random.uniform(0, base_delay * (2 ** attempt))
                logger.warning(f"Request error: {e}, waiting {delay:.1f}s (attempt {attempt})")
                await asyncio.sleep(delay)
        
        raise Exception(f"Max retries ({max_retries}) exceeded")
    
    def _auth_headers(self, extra: Dict[str, str] = None) -> Dict[str, str]:
        """Get headers with authentication"""
        headers = {"Content-Type": "application/json"}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        if extra:
            headers.update(extra)
        return headers

    async def cached_request(self, method: str, endpoint: str, data: Dict = None,
                           ttl_seconds: float = 0, coalesce_key: str = None) -> Dict[str, Any]:
        """
        Make cached and coalesced API request

        Args:
            method: HTTP method
            endpoint: API endpoint (will be prefixed with base_url)
            data: Request payload
            ttl_seconds: Cache TTL (0 = no cache)
            coalesce_key: Key for request coalescing (None = no coalescing)
        """
        url = f"{self.base_url}{endpoint}"

        # Check cache first
        if ttl_seconds > 0:
            cache_key = self._cache_key(method, url, data)
            cached_result = self._get_cached(cache_key)
            if cached_result is not None:
                return cached_result

        # Define the actual request function
        async def make_request():
            headers = self._auth_headers()
            kwargs = {"headers": headers}
            if data:
                kwargs["json"] = data

            response = await self._request_with_backoff(method, url, **kwargs)
            result = response.json()

            # Cache the result if TTL specified
            if ttl_seconds > 0:
                self._set_cache(cache_key, result, ttl_seconds)

            return result

        # Use circuit breaker and optionally coalesce
        if coalesce_key:
            return await self.coalescer.coalesce(coalesce_key, make_request)
        else:
            return self.circuit_breaker.call(lambda: asyncio.run(make_request()))

    # High-level API methods with optimized caching and coalescing

    async def search_contracts(self, search_text: str = "", live: bool = False) -> List[Dict[str, Any]]:
        """Search contracts with 10-minute cache and coalescing"""
        coalesce_key = f"contracts:{search_text}:{live}"
        result = await self.cached_request(
            "POST",
            "/api/Contract/search",
            {"searchText": search_text, "live": live},
            ttl_seconds=600,  # 10 minutes
            coalesce_key=coalesce_key
        )
        return result.get("contracts", []) if isinstance(result, dict) else result

    async def search_accounts(self, only_active: bool = True) -> List[Dict[str, Any]]:
        """Search accounts with 15-second cache and coalescing"""
        coalesce_key = f"accounts:{only_active}"
        result = await self.cached_request(
            "POST",
            "/api/Account/search",
            {"onlyActiveAccounts": only_active},
            ttl_seconds=15,  # 15 seconds
            coalesce_key=coalesce_key
        )
        return result.get("accounts", []) if isinstance(result, dict) else result

    async def get_historical_bars(self, contract_id: str, start_iso: str, end_iso: str,
                                unit: int = 2, unit_number: int = 1, limit: int = 20000) -> List[Dict[str, Any]]:
        """Get historical bars with session-based caching"""
        # Cache historical data for the entire session (no TTL expiry during session)
        cache_key = f"hist_bars:{contract_id}:{start_iso}:{end_iso}:{unit}:{unit_number}"

        # Check if we already have this data cached
        with self.cache_lock:
            entry = self.cache.get(cache_key)
            if entry:  # Historical data doesn't expire during session
                logger.debug(f"Using cached historical bars: {cache_key}")
                return entry.data

        result = await self.cached_request(
            "POST",
            "/api/History/retrieveBars",
            {
                "contractId": contract_id,
                "live": False,
                "startTime": start_iso,
                "endTime": end_iso,
                "unit": unit,
                "unitNumber": unit_number,
                "limit": limit,
                "includePartialBar": False
            },
            ttl_seconds=0  # We handle caching manually for historical data
        )

        bars = result.get("bars", []) if isinstance(result, dict) else result

        # Cache historical data indefinitely for this session
        with self.cache_lock:
            self.cache[cache_key] = CacheEntry(bars, float('inf'))

        return bars

    async def get_live_bars(self, contract_id: str, start_iso: str, end_iso: str,
                          unit: int = 2, unit_number: int = 1, limit: int = 20000) -> List[Dict[str, Any]]:
        """Get live bars with adaptive polling"""
        result = await self.cached_request(
            "POST",
            "/api/History/retrieveBars",
            {
                "contractId": contract_id,
                "live": True,
                "startTime": start_iso,
                "endTime": end_iso,
                "unit": unit,
                "unitNumber": unit_number,
                "limit": limit,
                "includePartialBar": True
            },
            ttl_seconds=0  # No caching for live data
        )

        bars = result.get("bars", []) if isinstance(result, dict) else result

        # Update adaptive poller based on data availability
        has_new_data = bool(bars)
        next_interval = self.live_bars_poller.update(has_new_data)

        return bars

    def get_next_live_poll_interval(self) -> float:
        """Get the next polling interval for live data"""
        return self.live_bars_poller.current_interval

    async def get_trades(self, account_id: str, start_timestamp: str = None,
                        start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """Get trades with short-term caching"""
        payload = {"accountId": account_id}
        if start_timestamp:
            payload["startTimestamp"] = start_timestamp
        if start_date:
            payload["startDate"] = start_date
        if end_date:
            payload["endDate"] = end_date

        result = await self.cached_request(
            "POST",
            "/api/Trade/search",
            payload,
            ttl_seconds=10  # 10 seconds cache for trades
        )
        return result.get("trades", []) if isinstance(result, dict) else result

    async def get_positions(self, account_id: str) -> List[Dict[str, Any]]:
        """Get open positions with short-term caching"""
        result = await self.cached_request(
            "POST",
            "/api/Position/searchOpen",
            {"accountId": account_id},
            ttl_seconds=5  # 5 seconds cache for positions
        )
        return result.get("positions", []) if isinstance(result, dict) else result

    async def get_orders(self, account_id: str) -> List[Dict[str, Any]]:
        """Get open orders with short-term caching"""
        result = await self.cached_request(
            "POST",
            "/api/Order/searchOpen",
            {"accountId": account_id},
            ttl_seconds=5  # 5 seconds cache for orders
        )
        return result.get("orders", []) if isinstance(result, dict) else result

    def clear_cache(self, pattern: str = None):
        """Clear cache entries, optionally matching a pattern"""
        with self.cache_lock:
            if pattern:
                keys_to_remove = [k for k in self.cache.keys() if pattern in k]
                for key in keys_to_remove:
                    del self.cache[key]
                logger.info(f"Cleared {len(keys_to_remove)} cache entries matching '{pattern}'")
            else:
                self.cache.clear()
                logger.info("Cleared all cache entries")

    async def close(self):
        """Clean up resources"""
        await self.client.aclose()


# Synchronous wrapper for compatibility with existing code
class SyncAPIClient:
    """Synchronous wrapper around OptimizedAPIClient for backward compatibility"""

    def __init__(self, base_url: str, token: str = None):
        self.async_client = OptimizedAPIClient(base_url, token)
        self._loop = None

    def _run_async(self, coro):
        """Run async coroutine in sync context"""
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # We're in an async context, need to use a new thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, coro)
                    return future.result()
            else:
                return loop.run_until_complete(coro)
        except RuntimeError:
            # No event loop, create one
            return asyncio.run(coro)

    def search_contracts(self, search_text: str = "", live: bool = False) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.search_contracts(search_text, live))

    def search_accounts(self, only_active: bool = True) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.search_accounts(only_active))

    def get_historical_bars(self, contract_id: str, start_iso: str, end_iso: str,
                          unit: int = 2, unit_number: int = 1, limit: int = 20000) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.get_historical_bars(
            contract_id, start_iso, end_iso, unit, unit_number, limit))

    def get_live_bars(self, contract_id: str, start_iso: str, end_iso: str,
                     unit: int = 2, unit_number: int = 1, limit: int = 20000) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.get_live_bars(
            contract_id, start_iso, end_iso, unit, unit_number, limit))

    def get_next_live_poll_interval(self) -> float:
        return self.async_client.get_next_live_poll_interval()

    def get_trades(self, account_id: str, start_timestamp: str = None,
                  start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.get_trades(account_id, start_timestamp, start_date, end_date))

    def get_positions(self, account_id: str) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.get_positions(account_id))

    def get_orders(self, account_id: str) -> List[Dict[str, Any]]:
        return self._run_async(self.async_client.get_orders(account_id))

    def clear_cache(self, pattern: str = None):
        self.async_client.clear_cache(pattern)

    def close(self):
        self._run_async(self.async_client.close())
