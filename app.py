# app.py
import os
from datetime import datetime, timedelta, timezone, date, time as dtime
import time
from typing import List, Dict, Any, Tuple
from zoneinfo import ZoneInfo
import logging

import pandas as pd
import streamlit as st
import httpx
from dotenv import load_dotenv

from broker_adapter import BrokerAdapter
from risk import ContractMeta
from registry import available as strategies_available, create as strategy_create
from backtest import run_replay
from api_service import (
    get_contracts, get_accounts, get_account_summary, get_positions,
    get_orders, get_trades, get_minute_bars_range, get_minute_bars_for_day,
    get_comprehensive_pnl, check_market_status, clear_cache, get_refresh_manager
)
from adaptive_polling import get_polling_integration

# ---------- basic config ----------
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()
st.set_page_config(
    page_title="TopstepX Pro Trading Bot",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)
ET = ZoneInfo("America/New_York")
today = datetime.now(ET).date()

# ---------- helpers ----------
def contract_label(contract):
    """Generate a human-readable label for a contract"""
    symbol = contract.get('symbol', 'Unknown')
    name = contract.get('name', '')
    description = contract.get('description', '')

    if name and name != symbol:
        return name

    if symbol and symbol != 'Unknown':
        if '.' in symbol:
            parts = symbol.split('.')
            for part in parts:
                if part in ['ENQ', 'NQ']:
                    return 'NQ'
                elif part in ['EMD', 'ES']:
                    return 'ES'
                elif part in ['MNQ']:
                    return 'MNQ'
                elif part in ['MES']:
                    return 'MES'
        if len(symbol) >= 2:
            base = symbol[:2]
            if base in ['NQ', 'ES']:
                return base
            elif symbol.startswith('MNQ'):
                return 'MNQ'
            elif symbol.startswith('MES'):
                return 'MES'

    if description:
        desc_upper = description.upper()
        if 'NASDAQ' in desc_upper and 'MICRO' in desc_upper:
            return 'MNQ'
        elif 'NASDAQ' in desc_upper:
            return 'NQ'
        elif ('S&P' in desc_upper or 'SP' in desc_upper) and 'MICRO' in desc_upper:
            return 'MES'
        elif 'S&P' in desc_upper or 'SP' in desc_upper:
            return 'ES'
        return description

    return symbol

def get_market_status():
    """Futures market status for TopstepX - operates nearly 24/7 with specific maintenance windows."""
    now_et = datetime.now(ET)
    weekday = now_et.weekday()  # Monday=0, Sunday=6
    current_time = now_et.time()

    # Debug logging
    logger.info(f"Futures market status check: {now_et.strftime('%A %Y-%m-%d %H:%M:%S %Z')}, weekday={weekday}, time={current_time}")

    # Futures market schedule:
    # Sunday: Opens at 6:00 PM ET
    # Monday-Thursday: Nearly 24/7 (brief maintenance 5:00-6:00 PM ET)
    # Friday: Closes at 5:00 PM ET
    # Saturday: Closed all day

    if weekday == 6:  # Sunday
        if current_time >= dtime(18, 0):  # 6:00 PM ET or later
            logger.info("Futures market status: open (Sunday evening session)")
            return "open"
        else:
            logger.info("Futures market status: closed (Sunday before 6 PM)")
            return "closed"
    elif weekday == 5:  # Saturday
        logger.info("Futures market status: closed (Saturday)")
        return "closed"
    elif weekday == 4:  # Friday
        if current_time <= dtime(17, 0):  # Before 5:00 PM ET
            logger.info("Futures market status: open (Friday session)")
            return "open"
        else:
            logger.info("Futures market status: closed (Friday after 5 PM)")
            return "closed"
    else:  # Monday-Thursday (0-3)
        # Brief maintenance window 5:00-6:00 PM ET
        if dtime(17, 0) <= current_time <= dtime(18, 0):
            logger.info("Futures market status: maintenance (daily 5-6 PM)")
            return "maintenance"
        else:
            logger.info("Futures market status: open (weekday session)")
            return "open"

def et_midnight(d: date) -> datetime:
    return datetime(d.year, d.month, d.day, 0, 0, 0, tzinfo=ET)

def api_paused() -> bool:
    """Check if API calls should be paused due to rate limiting"""
    until = st.session_state.get("api_pause_until")
    return bool(until and datetime.now(ET) < until)

def pause_api(seconds: float):
    """Pause API calls for the specified duration"""
    st.session_state.api_pause_until = datetime.now(ET) + timedelta(seconds=seconds)
    logger.warning(f"API paused for {seconds:.1f}s due to rate limiting")

# ---------- data access (optimized) ----------
# Note: Data access functions moved to api_service.py for better organization and optimization

def ensure_ohlcv(df: pd.DataFrame) -> pd.DataFrame:
    """Ensure DataFrame has all required OHLCV columns"""
    if df.empty:
        return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])
    for col in ["o", "h", "l", "c", "v"]:
        if col not in df.columns:
            df[col] = 0 if col == "v" else 0.0
    return df





def ensure_ohlcv(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])
    for col in ["o", "h", "l", "c", "v"]:
        if col not in df.columns:
            df[col] = 0 if col == "v" else 0.0
    return df

def compute_prev_day_levels(df_prev: pd.DataFrame):
    """Return (pdh, pdl, sessions_dict)."""
    if df_prev.empty:
        return None, None, {}
    df = df_prev.copy()
    if 'time_et' in df.columns:
        df['time_et'] = pd.to_datetime(df['time_et'])
        df['hour'] = df['time_et'].dt.hour
    pdh = df["h"].max()
    pdl = df["l"].min()

    sessions: Dict[str, Tuple[float, float]] = {}
    try:
        asia = df[(df['hour'] >= 19) | (df['hour'] <= 4)]
        if not asia.empty:
            sessions["Asia"] = (asia["h"].max(), asia["l"].min())
        london = df[(df['hour'] >= 3) & (df['hour'] <= 12)]
        if not london.empty:
            sessions["London"] = (london["h"].max(), london["l"].min())
        ny = df[(df['hour'] >= 8) & (df['hour'] <= 17)]
        sessions["New York"] = (ny["h"].max(), ny["l"].min()) if not ny.empty else (pdh, pdl)
    except Exception as e:
        logger.error(f"Error computing session levels: {e}")
        sessions = {"New York": (pdh, pdl)}
    return pdh, pdl, sessions

def resample_ohlc(df: pd.DataFrame, freq: str) -> pd.DataFrame:
    if df.empty:
        return pd.DataFrame()
    try:
        d = df.copy()
        d['time_et'] = pd.to_datetime(d['time_et'])
        d = d.set_index('time_et')
        freq_fixed = freq.replace('T', 'min')
        res = d.resample(freq_fixed).agg({'o':'first','h':'max','l':'min','c':'last','v':'sum'}).dropna()
        return res.reset_index()
    except Exception as e:
        logger.error(f"Error resampling data: {e}")
        return pd.DataFrame()

# ---------- initial session state ----------
if 'realtime_enabled' not in st.session_state:
    st.session_state.realtime_enabled = True
if 'realtime_mode' not in st.session_state:
    st.session_state.realtime_mode = 'polling'
if 'realtime' not in st.session_state:
    st.session_state.realtime = None
if 'bot_active' not in st.session_state:
    st.session_state.bot_active = False
if 'bot_start_time' not in st.session_state:
    st.session_state.bot_start_time = None
if 'bot_scan_count' not in st.session_state:
    st.session_state.bot_scan_count = 0
if 'bot_trades' not in st.session_state:
    st.session_state.bot_trades = []
if 'bot_log' not in st.session_state:
    st.session_state.bot_log = []

# ---------- styles ----------
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    .main-header{background:linear-gradient(135deg,#1e3c72 0%,#2a5298 100%);padding:2rem;border-radius:15px;color:white;text-align:center;margin-bottom:2rem;box-shadow:0 8px 32px rgba(0,0,0,0.1)}
    .metric-card{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:1.5rem;border-radius:12px;color:white;text-align:center;margin:.5rem 0;box-shadow:0 4px 20px rgba(0,0,0,0.1);transition:transform .2s ease}
    .metric-card:hover{transform:translateY(-2px)}
    .level-card{background:rgba(255,255,255,0.05);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);padding:1rem;border-radius:10px;margin:.5rem 0}
    .signal-card{background:linear-gradient(135deg,#ff6b6b 0%,#ee5a24 100%);padding:2rem;border-radius:15px;color:white;text-align:center;box-shadow:0 8px 32px rgba(238,90,36,0.3);margin:1rem 0}
    .signal-card.long{background:linear-gradient(135deg,#00b894 0%,#00a085 100%);box-shadow:0 8px 32px rgba(0,184,148,0.3)}
    .confluence-item{background:rgba(255,255,255,0.1);padding:.8rem;border-radius:8px;margin:.3rem 0;border-left:4px solid #00b894}
    .status-active{color:#00ff88;font-weight:bold;text-shadow:0 0 10px rgba(0,255,136,0.5)}
    .status-idle{color:#ffa726;font-weight:bold;text-shadow:0 0 10px rgba(255,167,38,0.5)}
    .status-inactive{color:#ff5252;font-weight:bold;text-shadow:0 0 10px rgba(255,82,82,0.5)}
    .trade-suggestion{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:1.5rem;border-radius:12px;color:white;box-shadow:0 6px 25px rgba(102,126,234,0.3)}
    .key-level{display:flex;justify-content:space-between;align-items:center;padding:.5rem;background:rgba(255,255,255,0.05);border-radius:6px;margin:.2rem 0;border-left:3px solid #00b894}
    .risk-metrics{background:linear-gradient(135deg,#2d3436 0%,#636e72 100%);padding:1rem;border-radius:10px;color:white;margin:.5rem 0}
</style>
""", unsafe_allow_html=True)

# ---------- sidebar ----------
with st.sidebar:
    st.markdown("### 🔐 Connection Status")

    # CRITICAL FIX: Cache broker instance to avoid repeated logins
    if "broker" not in st.session_state:
        try:
            st.session_state.broker = BrokerAdapter(base_url=os.getenv("PROJECTX_BASE_URL"))
            st.session_state.broker.login_with_api_key()
            st.success("✅ Authenticated")
        except Exception as e:
            st.error(f"❌ Auth error: {e}")
            st.stop()
    else:
        st.success("✅ Authenticated (cached)")

    broker = st.session_state.broker

    st.markdown("---")

    st.markdown("### 💼 Account Selection")
    try:
        accounts = broker.list_accounts(only_active=True)
        if accounts:
            acct_options = []
            for a in accounts:
                name = a.get("name", "Unknown")
                id_str = a.get("id", "N/A")
                balance = a.get("balance", 0)
                acct_options.append(f"💰 {name} [{id_str}] - ${balance:,.2f}")
            acct_map = {opt: acc for opt, acc in zip(acct_options, accounts)}
            acct_label = st.selectbox("Select Account", options=acct_options)
            account = acct_map.get(acct_label)
        else:
            st.warning("No active accounts found")
            account = None
    except Exception as e:
        st.error(f"❌ Accounts error: {e}")
        account = None

    if not account:
        st.info("Please select an account to continue.")
        st.stop()

    account_id = account.get("id")
    if not account_id:
        st.error("Account ID not found")
        st.stop()

    st.markdown("---")

    st.markdown("### 📊 Contract Selection")
    with st.spinner("Loading contracts..."):
        target_symbols = ["NQ", "MNQ", "ES", "MES"]
        contracts: List[Dict[str, Any]] = []
        for symbol in target_symbols:
            try:
                # Use optimized API with caching and coalescing
                live_contracts = get_contracts(symbol, live=True)
                hist_contracts = get_contracts(symbol, live=False)
                for c in live_contracts + hist_contracts:
                    sym = (c.get("name") or c.get("symbol") or "").upper()
                    desc = (c.get("description") or "").upper()
                    if (sym == symbol or
                        (symbol == "NQ" and "NASDAQ" in desc and "MICRO" not in desc) or
                        (symbol == "MNQ" and "NASDAQ" in desc and "MICRO" in desc) or
                        (symbol == "ES" and ("S&P" in desc or "SP" in desc) and "MICRO" not in desc) or
                        (symbol == "MES" and ("S&P" in desc or "SP" in desc) and "MICRO" in desc)):
                        if not any(existing.get("id") == c.get("id") for existing in contracts):
                            contracts.append(c)
            except Exception as e:
                st.error(f"Error loading {symbol}: {e}")

    if contracts:
        contract_options = []
        for c in contracts:
            label = contract_label(c)
            tick_value = c.get("tickValue", "N/A")
            contract_options.append(f"{label} (${tick_value}/tick)")
        contract_map = {opt: cont for opt, cont in zip(contract_options, contracts)}
        default_idx = 0
        for i, opt in enumerate(contract_options):
            # CRITICAL FIX: Look for "NQ" in the option text (not "NQ -")
            if "NQ" in opt:
                default_idx = i
                break
        selected_contract_label = st.selectbox("Select Contract", options=contract_options, index=default_idx)
        contract = contract_map[selected_contract_label]
        contract_id = contract.get("id") or contract.get("symbol")
        if not contract_id:
            st.error("Contract ID not found")
            st.stop()

        st.info(f"""
        **Contract Details:**
        - Symbol: {contract.get('symbol', 'N/A')}
        - Tick Size: ${contract.get('tickSize', 'N/A')}
        - Tick Value: ${contract.get('tickValue', 'N/A')}
        """)
    else:
        st.error("❌ No contracts found")
        st.stop()

    st.markdown("---")

    st.markdown("### 📡 Real-time Data Status")
    mode = st.session_state.get('realtime_mode', 'polling')
    if mode == "signalr":
        rt = st.session_state.get('realtime')
        if rt and rt.connected:
            st.success("🟢 SignalR Active - Live data streaming")
        else:
            st.warning("🟡 SignalR Disconnected - Falling back to polling")
            st.session_state.realtime_mode = 'polling'
    elif mode == "polling":
        # Use adaptive polling integration
        polling_integration = get_polling_integration()
        market_status, market_open = check_market_status()

        st.info("🔄 REST Polling Active - Using adaptive intervals based on data availability")

        # Show polling status
        polling_integration.show_polling_debug_info()

        # Check if it's time to refresh
        if polling_integration.setup_auto_refresh(market_open):
            logger.info("Auto-refreshing with adaptive polling")
            st.rerun()
    else:
        st.warning("❓ Unknown mode - Defaulting to polling")
        st.session_state.realtime_mode = 'polling'

    if st.button("🔍 Debug Account Structure"):
        try:
            account_debug = broker.debug_account_structure()
            st.json(account_debug)
        except Exception as e:
            st.error(f"Account debug failed: {e}")

    if st.button("🔍 Check Available Endpoints"):
        try:
            available = broker.check_available_endpoints()
            if available:
                st.success("Found these endpoints:")
                for endpoint in available:
                    st.write(f"• {endpoint}")
            else:
                st.info("No additional endpoints found")
        except Exception as e:
            st.error(f"Endpoint check failed: {e}")

    with st.expander("🔧 Simple API Test"):
        if st.button("🔍 Test Basic Endpoints"):
            try:
                with st.spinner("Testing basic endpoints..."):
                    test_results = broker.simple_endpoint_test()
                working = {k: v for k, v in test_results.items() if v.get("success", False)}
                failing = {k: v for k, v in test_results.items() if not v.get("success", False)}
                if working:
                    st.success(f"✅ {len(working)} endpoints working:")
                    for endpoint, result in working.items():
                        st.write(f"• {endpoint} - {result['status']}")
                if failing:
                    st.warning(f"❌ {len(failing)} endpoints failing:")
                    for endpoint, result in failing.items():
                        status = result.get('status', 'Unknown')
                        st.write(f"• {endpoint} - {status}")
            except Exception as e:
                st.error(f"Test failed: {e}")

    with st.expander("� Market Status Override"):
        st.write("**Current Status:**")
        current_status = get_market_status()
        st.write(f"• Detected: {current_status}")

        override_status = st.selectbox(
            "Override market status for testing:",
            ["auto", "open", "closed", "maintenance", "pre_market", "after_hours"],
            index=0
        )

        if override_status != "auto":
            st.session_state.market_status_override = override_status
            st.success(f"Market status overridden to: {override_status}")
        elif "market_status_override" in st.session_state:
            del st.session_state.market_status_override
            st.info("Using automatic market status detection")

    with st.expander("�🔧 ProjectX API Test"):
        if st.button("🔍 Test ProjectX Endpoints"):
            try:
                with st.spinner("Testing ProjectX API endpoints..."):
                    test_results = broker.test_projectx_endpoints(account_id)
                working = {k: v for k, v in test_results.items() if v.get("success", False)}
                failing = {k: v for k, v in test_results.items() if not v.get("success", False)}
                if working:
                    st.success(f"✅ {len(working)} endpoints working:")
                    for endpoint, result in working.items():
                        st.write(f"• **{endpoint}** - Status: {result['status']}")
                        if 'keys' in result:
                            st.write(f"  Response keys: {result['keys']}")
                        elif 'count' in result:
                            st.write(f"  Items returned: {result['count']}")
                if failing:
                    st.warning(f"❌ {len(failing)} endpoints failing:")
                    for endpoint, result in failing.items():
                        status = result.get('status', 'Unknown')
                        st.write(f"• **{endpoint}** - Status: {status}")
                        if 'error' in result:
                            st.write(f"  Error: {result['error']}")
            except Exception as e:
                st.error(f"ProjectX API test failed: {e}")

    if st.button("🔍 Debug Account Data"):
        try:
            fresh_data = get_account_summary(account_id)
            st.json(fresh_data)

            # Also check what fields are available
            if fresh_data:
                st.write("**Available fields:**")
                for key, value in fresh_data.items():
                    st.write(f"• `{key}`: {value}")
        except Exception as e:
            st.error(f"Account data debug failed: {e}")

    if st.button("🔍 Debug Trade Data"):
        try:
            from datetime import date
            today = date.today()

            # Try to get today's trades
            url = broker._api("/Trade/search")
            headers = broker._auth_headers({"content-type": "application/json"})

            payload = {
                "accountId": account_id,
                "startDate": today.isoformat(),
                "endDate": today.isoformat()
            }

            r = broker._client.post(url, headers=headers, json=payload)
            r.raise_for_status()

            data = r.json()
            trades = data if isinstance(data, list) else data.get("trades", data.get("data", []))

            st.write(f"**Found {len(trades)} trades for {today}**")
            if trades:
                st.write("**Sample trade structure:**")
                st.json(trades[0])

                st.write("**All trade fields:**")
                all_fields = set()
                for trade in trades:
                    all_fields.update(trade.keys())
                st.write(list(sorted(all_fields)))

                # Calculate P&L breakdown
                total_pnl = 0.0
                total_fees = 0.0
                pnl_trades = 0
                for trade in trades:
                    if "profitAndLoss" in trade and trade["profitAndLoss"] is not None:
                        total_pnl += float(trade["profitAndLoss"])
                        pnl_trades += 1
                    if "fees" in trade and trade["fees"] is not None:
                        total_fees += float(trade["fees"])

                st.write(f"**P&L Summary:**")
                st.write(f"• Total P&L: ${total_pnl:,.2f} (from {pnl_trades} trades)")
                st.write(f"• Total Fees: ${total_fees:,.2f}")
                st.write(f"• Net P&L: ${total_pnl - total_fees:,.2f}")

        except Exception as e:
            st.error(f"Trade data debug failed: {e}")

    if st.button("🔍 Debug P&L Breakdown"):
        try:
            pnl_data = get_comprehensive_pnl(account_id)
            st.write("**Comprehensive P&L Data:**")
            st.json(pnl_data)

            # Also show last stored P&L data
            if hasattr(st.session_state, 'last_pnl_data'):
                st.write("**Last P&L Calculation:**")
                st.json(st.session_state.last_pnl_data)

        except Exception as e:
            st.error(f"P&L debug failed: {e}")

    # Cache Management Section
    st.markdown("---")
    st.markdown("### 🗄️ Cache Management")

    cache_col1, cache_col2, cache_col3 = st.columns(3)

    with cache_col1:
        if st.button("🧹 Clear All Caches", help="Clear both Streamlit and API caches"):
            clear_cache()
            st.success("All caches cleared!")
            st.rerun()

    with cache_col2:
        if st.button("📊 Clear Contract Cache", help="Clear only contract search cache"):
            clear_cache("contracts")
            st.success("Contract cache cleared!")

    with cache_col3:
        if st.button("💰 Clear Account Cache", help="Clear only account data cache"):
            clear_cache("account")
            st.success("Account cache cleared!")

    # Show cache statistics
    with st.expander("📈 Cache Statistics"):
        from api_service import get_api_client
        try:
            client = get_api_client()
            cache_stats = {
                "Total cached items": len(client.async_client.cache),
                "Cache hits": "Available in detailed logs",
                "Polling mode": st.session_state.get('polling_mode', 'normal'),
                "Empty streak": st.session_state.get('empty_streak', 0)
            }

            for key, value in cache_stats.items():
                st.write(f"**{key}:** {value}")

        except Exception as e:
            st.write(f"Cache stats unavailable: {e}")

# ---------- tabs ----------
tab_dash, tab_bot, tab_history, tab_replay, tab_logs = st.tabs(["Dashboard", "Bot Control", "Trading History", "Replay", "Logs"])

# ---------- dashboard ----------
with tab_dash:
    account_name = account.get("name", "Unknown Account") if account else "Unknown Account"
    st.title(f"TopstepX Bot - {account_name}")

    def get_market_status_display():
        # Check for override first
        if "market_status_override" in st.session_state:
            status = st.session_state.market_status_override
        else:
            status = get_market_status()

        now_et = datetime.now(ET)
        status_map = {
            "open": ("🟢 FUTURES OPEN", "status-active"),
            "closed": ("🔴 FUTURES CLOSED", "status-inactive"),
            "maintenance": ("🟡 MAINTENANCE", "status-idle"),
            "pre_market": ("🟡 PRE-MARKET", "status-idle"),
            "after_hours": ("🟠 AFTER HOURS", "status-idle"),
        }
        display, css_class = status_map.get(status, ("❓ UNKNOWN", "status-inactive"))

        # Add override indicator
        if "market_status_override" in st.session_state:
            display += " (OVERRIDE)"

        current_time = now_et.strftime('%I:%M:%S %p ET')
        return display, current_time, css_class

    market_display, current_time, status_class = get_market_status_display()
    status_col1, status_col2 = st.columns([2, 1])
    with status_col1:
        st.markdown(f'<div class="{status_class}" style="font-size: 1.2em; padding: .5rem; text-align: center; border-radius: .5rem; margin-bottom: 1rem;">{market_display}</div>', unsafe_allow_html=True)
    with status_col2:
        st.markdown(f'<div style="font-size: 1.1em; text-align: center; padding: .5rem;">🕐 {current_time}</div>', unsafe_allow_html=True)

    with st.spinner("🔄 Loading live market data..."):
        # Use optimized API service with caching and adaptive polling
        df_today_live = get_minute_bars_for_day(contract_id, today, live=True)
        df_today_hist = get_minute_bars_for_day(contract_id, today, live=False)

        # Update adaptive polling based on data availability
        polling_integration = get_polling_integration()
        polling_integration.handle_data_result(df_today_live, "live_bars")

        df_prev = pd.DataFrame()
        days_back = 1
        while df_prev.empty and days_back <= 5:
            prev_day = today - timedelta(days=days_back)
            df_prev = get_minute_bars_for_day(contract_id, prev_day, live=False)
            if not df_prev.empty:
                logger.info(f"Found previous day data for {prev_day} ({len(df_prev)} bars)")
                break
            days_back += 1

    if not df_today_live.empty:
        df_today = ensure_ohlcv(df_today_live)
        logger.info(f"Using live data for today: {len(df_today)} bars")
    elif not df_today_hist.empty:
        df_today = ensure_ohlcv(df_today_hist)
        logger.warning(f"Live data unavailable, using historical data: {len(df_today)} bars")
    else:
        df_today = pd.DataFrame(columns=["time_et", "o", "h", "l", "c", "v"])
        logger.error("No data available for today")

    df_prev = ensure_ohlcv(df_prev)
    pdh, pdl, sessions = compute_prev_day_levels(df_prev)

    data_status_col1, data_status_col2, data_status_col3 = st.columns(3)
    with data_status_col1:
        if not df_today_live.empty:
            st.success("🟢 Live Data Active")
        else:
            st.warning("📊 Using Historical Data")
    with data_status_col2:
        st.info(f"📊 {len(df_today)} bars loaded")
    with data_status_col3:
        if not df_today.empty and "time_et" in df_today.columns:
            last_update = df_today["time_et"].iloc[-1]
            if isinstance(last_update, str):
                last_update = pd.to_datetime(last_update)
            minutes_ago = (datetime.now(ET) - last_update.tz_convert(ET)).total_seconds() / 60
            st.info(f"⏱️ {minutes_ago:.0f}min ago")

    last_price = float(df_today["c"].iloc[-1]) if not df_today.empty else None

    st.subheader("📊 Key Market Levels")
    level_col1, level_col2, level_col3, level_col4, level_col5 = st.columns(5)
    with level_col1:
        sym = contract.get("name", contract.get("symbol", "?"))
        st.metric("📈 Symbol", sym)
    with level_col2:
        if last_price is not None:
            price_display = f"${last_price:,.2f}" + (" 🔴" if not df_today_live.empty else " 📊")
            st.metric("💰 Current Price", price_display)
        else:
            st.metric("💰 Current Price", "—")
    with level_col3:
        pdh_distance = f"({abs(last_price - pdh):,.1f} pts)" if last_price and pdh else ""
        st.metric("📈 Previous Day High", f"${pdh:,.2f}" if pdh is not None else "—", delta=pdh_distance or None)
    with level_col4:
        pdl_distance = f"({abs(last_price - pdl):,.1f} pts)" if last_price and pdl else ""
        st.metric("📉 Previous Day Low", f"${pdl:,.2f}" if pdl is not None else "—", delta=pdl_distance or None)
    with level_col5:
        daily_range = (pdh - pdl) if (pdh and pdl) else 0
        st.metric("📏 Daily Range", f"{daily_range:,.1f} pts" if daily_range > 0 else "—")

    if sessions:
        st.markdown("**📅 Previous Session Levels**")
        session_data = []
        for session_name, (high, low) in sessions.items():
            if high is not None and low is not None:
                range_pts = high - low
                hi_distance = abs(last_price - high) if last_price else 0
                lo_distance = abs(last_price - low) if last_price else 0
                session_data.append({
                    "Session": session_name,
                    "High": f"${high:,.2f}",
                    "Low": f"${low:,.2f}",
                    "Range": f"{range_pts:,.1f} pts",
                    "Distance to High": f"{hi_distance:,.1f} pts",
                    "Distance to Low": f"{lo_distance:,.1f} pts"
                })
        if session_data:
            st.dataframe(pd.DataFrame(session_data), use_container_width=True, hide_index=True)

    st.divider()

    account_header_col1, account_header_col2 = st.columns([3, 1])
    with account_header_col1:
        st.subheader("💰 Account Overview")
    with account_header_col2:
        if st.button("🔄 Refresh Account", help="Fetch latest account data"):
            # Clear both Streamlit cache and optimized API cache
            clear_cache()
            st.rerun()
    try:
        account_details = account or {}
        positions, orders = [], []
        positions_error = orders_error = None

        # Clear any cached account data to ensure fresh fetch
        if hasattr(st, 'cache_data'):
            st.cache_data.clear()

        try:
            positions = broker.list_positions(account_id) or []
            if not isinstance(positions, list):
                positions = []
        except Exception as pos_error:
            positions_error = str(pos_error)
            logger.warning(f"Positions unavailable: {pos_error}")
        try:
            orders = broker.list_orders(account_id) or []
            if not isinstance(orders, list):
                orders = []
        except Exception as order_error:
            orders_error = str(order_error)
            logger.warning(f"Orders unavailable: {order_error}")

        # Get fresh account data for accurate balances with live polling
        fresh_account_data = {}
        try:
            logger.info(f"Fetching live account data for account {account_id}")
            fresh_account_data = get_account_summary(account_id)
            if fresh_account_data:
                account_details.update(fresh_account_data)
                logger.info(f"Updated account with fresh data: {list(fresh_account_data.keys())}")
            else:
                logger.warning("No fresh account data returned")
        except Exception as e:
            logger.warning(f"Could not fetch fresh account data: {e}")
            fresh_account_data = {}

        acc_col1, acc_col2, acc_col3, acc_col4 = st.columns(4)
        with acc_col1:
            # Try multiple balance fields with live data priority
            balance = (fresh_account_data.get("balance") if fresh_account_data else None) or \
                     (fresh_account_data.get("equity") if fresh_account_data else None) or \
                     (fresh_account_data.get("netLiquidation") if fresh_account_data else None) or \
                     (fresh_account_data.get("accountValue") if fresh_account_data else None) or \
                     (account_details.get("balance") or
                      account_details.get("equity") or
                      account_details.get("netLiquidation") or
                      account_details.get("accountValue") or 0)

            # Add live indicator
            balance_label = "💵 Account Balance" + (" 🔴" if fresh_account_data else " 📊")
            st.metric(balance_label, f"${balance:,.2f}")

        with acc_col2:
            daily_pnl = 0.0
            pnl_source = "cached"

            # Try to get comprehensive P&L data with caching
            try:
                logger.info(f"Fetching comprehensive P&L for account {account_id}")
                # Check if API is paused
                if api_paused():
                    logger.info("API paused, skipping P&L fetch")
                    daily_pnl = 0.0
                    pnl_source = "paused"
                else:
                    pnl_data = get_comprehensive_pnl(account_id)
                    daily_pnl = pnl_data["realized_pnl"]
                    pnl_source = "comprehensive"

                # Store P&L data in session state for debugging
                st.session_state.last_pnl_data = pnl_data

                logger.info(f"Comprehensive P&L: Realized={daily_pnl}, Unrealized={pnl_data['unrealized_pnl']}, Total={pnl_data['total_pnl']}")

            except Exception as pnl_error:
                logger.error(f"Comprehensive P&L calculation failed: {pnl_error}")
                try:
                    # Fallback to simple method
                    daily_pnl = broker.realized_pnl_today(account_id)
                    pnl_source = "fallback"
                except Exception as alt_error:
                    logger.error(f"Fallback P&L calculation failed: {alt_error}")
                    daily_pnl = 0.0
                    pnl_source = "failed"

            pnl_delta = f"{(daily_pnl/balance)*100:+.2f}%" if balance > 0 and daily_pnl != 0 else None
            pnl_label = f"📈 Daily P&L ({pnl_source})"
            st.metric(pnl_label, f"${daily_pnl:+,.2f}", delta=pnl_delta, delta_color=("normal" if daily_pnl >= 0 else "inverse"))
        with acc_col3:
            unrealized_pnl = sum(float(pos.get("unrealizedPnl", 0)) for pos in positions) if positions else 0.0
            st.metric("📊 Unrealized P&L", f"${unrealized_pnl:+,.2f}", delta_color=("normal" if unrealized_pnl >= 0 else "inverse"))
        with acc_col4:
            active_positions = len([p for p in positions if float(p.get("qty", 0)) != 0]) if positions else 0
            st.metric("🎯 Active Positions", active_positions)

        pos_col, order_col = st.columns(2)
        with pos_col:
            if positions:
                active_pos = [pos for pos in positions if float(pos.get("qty", 0)) != 0]
                if active_pos:
                    st.markdown("**📍 Current Positions**")
                    pos_data = []
                    for pos in active_pos:
                        pnl = float(pos.get('unrealizedPnl', 0))
                        pnl_color = "🟢" if pnl >= 0 else "🔴"
                        pos_data.append({
                            "Symbol": pos.get("symbol", ""),
                            "Qty": int(pos.get("qty", 0)),
                            "Avg Price": f"${float(pos.get('avgPrice', 0)):.2f}",
                            "P&L": f"{pnl_color} ${pnl:+.2f}"
                        })
                    st.dataframe(pd.DataFrame(pos_data), use_container_width=True, hide_index=True)
                else:
                    st.info("✅ No active positions")
            else:
                if positions_error:
                    st.warning(f"⚠️ Position data error: {positions_error[:50]}...")
                else:
                    st.info("✅ No positions found")
        with order_col:
            if orders:
                recent_orders = orders[-3:]
                if recent_orders:
                    st.markdown("**📋 Recent Orders**")
                    order_data = []
                    for order in recent_orders:
                        side_value = order.get("side", "")
                        try:
                            if isinstance(side_value, int):
                                side_str = "buy" if side_value == 1 else "sell" if side_value == 2 else str(side_value)
                            elif side_value:
                                side_str = str(side_value).lower()
                            else:
                                side_str = ""
                        except (AttributeError, TypeError):
                            side_str = str(side_value) if side_value else ""
                        side_emoji = "🟢" if "buy" in side_str else "🔴"
                        order_data.append({
                            "Time": order.get("timestamp", "")[:10] if order.get("timestamp") else "",
                            "Side": f"{side_emoji} {side_str.title()}",
                            "Symbol": order.get("symbol", ""),
                            "Status": order.get("status", "")
                        })
                    st.dataframe(pd.DataFrame(order_data), use_container_width=True, hide_index=True)
                else:
                    st.info("✅ No recent orders")
            else:
                if orders_error:
                    st.warning(f"⚠️ Order data error: {orders_error[:50]}...")
                else:
                    st.info("✅ No orders found")
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        st.error(f"Failed to load account information: {e}")
        logger.error(f"Account info error: {e}")
        logger.error(f"Full traceback: {error_details}")
        print(f"ACCOUNT ERROR: {e}")
        print(f"TRACEBACK: {error_details}")

    df_htf = get_minute_bars_range(contract_id, today - timedelta(days=7), today, live=False)
    df_htf = ensure_ohlcv(df_htf if not df_htf.empty else df_prev)

    asia_hi = sessions.get("Asia", (None, None))[0] if sessions else None
    asia_lo = sessions.get("Asia", (None, None))[1] if sessions else None
    london_hi = sessions.get("London", (None, None))[0] if sessions else None
    london_lo = sessions.get("London", (None, None))[1] if sessions else None
    ny_hi = sessions.get("New York", (None, None))[0] if sessions else None
    ny_lo = sessions.get("New York", (None, None))[1] if sessions else None

    h1 = resample_ohlc(df_htf, "60min")
    h4 = resample_ohlc(df_htf, "240min")
    htf_levels: List[float] = []
    for frame in (h1, h4):
        if not frame.empty:
            htf_levels.extend([float(frame["h"].max()), float(frame["l"].min())])
    for lvl in (pdh, pdl, ny_hi, ny_lo):
        if lvl is not None:
            htf_levels.append(lvl)

    names = strategies_available()
    st.subheader("🎯 Trading Signal & Execution")
    if names:
        strat = strategy_create("Enhanced SMC",
                                min_confidence_to_trade=0.55,
                                use_sweep=True, use_mss=True, use_fvg=True,
                                use_order_blocks=True, use_htf_bias=True,
                                use_proximity=True, use_session_filter=True)
    else:
        from enhanced_smc import build as smc_build
        strat = smc_build(min_confidence_to_trade=0.55,
                          use_sweep=True, use_mss=True, use_fvg=True,
                          use_order_blocks=True, use_htf_bias=True,
                          use_proximity=True, use_session_filter=True)

    meta = ContractMeta(
        tick_size=contract.get("tickSize", 0.25),
        tick_value=contract.get("tickValue", 5.0)
    )
    strat.prime(meta=meta, htf_bias=None, htf_levels=htf_levels)

    last_price = float(df_today["c"].iloc[-1]) if not df_today.empty else None
    guard = {"allow_long": True, "allow_short": True, "session_ok": True, "dollar_risk_per_trade": 100.0, "max_contracts": 1}

    res = strat.evaluate(
        last_price,
        df_today[["o", "h", "l", "c", "v"]],
        {"pdh": pdh, "pdl": pdl, "session_hi": ny_hi, "session_lo": ny_lo,
         "asia_hi": asia_hi, "asia_lo": asia_lo, "london_hi": london_hi, "london_lo": london_lo},
        meta,
        guard,
    )

    signal_col, analysis_col = st.columns([1, 1])
    with signal_col:
        st.markdown("**🎯 Current Signal**")
        signal = res.get("signal")
        confidence = res.get("confidence", 0.0)

        if signal:
            signal_class = "long" if signal == "long" else "short"
            signal_emoji = "🚀" if signal == "long" else "🔻"
            conf_color = "#00b894" if confidence >= 0.55 else "#ffa726"
            st.markdown(f"""
            <div class="signal-card {signal_class}" style="padding:1rem;border-radius:.5rem;margin:.5rem 0;text-align:center;background:linear-gradient(135deg,rgba(0,184,148,0.1),rgba(0,184,148,0.05));">
                <h2 style="margin:0;color:{conf_color};">{signal_emoji} {signal.upper()} SIGNAL</h2>
                <h3 style="margin:.5rem 0;color:{conf_color};">Confidence: {confidence:.1%}</h3>
                <p style="margin:0;">{'✅ Above threshold - Ready to execute!' if confidence >= 0.55 else '⏳ Below threshold - Monitoring'}</p>
            </div>
            """, unsafe_allow_html=True)

            suggested = res.get("suggested", {})
            if suggested:
                risk_reward = suggested.get("risk_reward", 0)
                potential_profit = abs(suggested["target"] - suggested["entry"]) * suggested["qty"] * meta.tick_value / meta.tick_size
                potential_loss = abs(suggested["stop"] - suggested["entry"]) * suggested["qty"] * meta.tick_value / meta.tick_size

                setup_col1, setup_col2, setup_col3 = st.columns(3)
                with setup_col1:
                    st.metric("📍 Entry", f"${suggested['entry']:,.2f}")
                with setup_col2:
                    st.metric("🛑 Stop", f"${suggested['stop']:,.2f}")
                with setup_col3:
                    st.metric("🎯 Target", f"${suggested['target']:,.2f}")

                rr_col1, rr_col2 = st.columns(2)
                with rr_col1:
                    st.metric("📊 Risk/Reward", f"1:{risk_reward:.1f}")
                with rr_col2:
                    st.metric("📦 Quantity", f"{suggested['qty']} contracts")

                st.markdown(f"""
                <div style="padding:.5rem;border-radius:.5rem;background:rgba(0,184,148,0.1);margin:.5rem 0;">
                    <strong>💰 Potential P&L:</strong>
                    <span style="color:#00b894;">+${potential_profit:,.0f}</span> /
                    <span style="color:#ff6b6b;">-${potential_loss:,.0f}</span>
                </div>
                """, unsafe_allow_html=True)

                if st.button(f"🎯 Execute {signal.upper()} Trade", type="primary", use_container_width=True):
                    try:
                        out = broker.place_bracket_idem(
                            account_id=account_id,
                            contract_id=contract_id,
                            side=("long" if signal == "long" else "short"),
                            qty=int(suggested["qty"]),
                            entry_type="MKT",
                            entry_price=None,
                            stop_price=float(suggested["stop"]),
                            target_price=float(suggested["target"]),
                            tag="enhanced_smc",
                        )
                        st.success("✅ Order executed successfully!")
                        st.json(out)
                    except Exception as e:
                        st.error(f"❌ Order failed: {e}")
        else:
            missing_conditions = []
            if confidence < 0.55:
                missing_conditions.append(f"Confidence too low ({confidence:.1%} < 55%)")
            analysis = res.get("analysis", {})
            if not analysis.get('sweep_detected'):
                missing_conditions.append("No liquidity sweep detected")
            if not analysis.get('fvg_active'):
                missing_conditions.append("No active FVG zones")
            if not analysis.get('order_blocks_nearby'):
                missing_conditions.append("No nearby order blocks")
            if not analysis.get('session_optimal'):
                missing_conditions.append("Outside optimal trading session")
            missing_text = " • ".join(missing_conditions[:3]) if missing_conditions else "Waiting for confluence alignment"
            st.markdown(f"""
            <div class="signal-card" style="padding:1rem;border-radius:.5rem;margin:.5rem 0;text-align:center;background:rgba(255,255,255,0.05);">
                <h2 style="margin:0;color:#888;">🔍 SCANNING MARKET</h2>
                <h3 style="margin:.5rem 0;color:#888;">No Signal Detected</h3>
                <p style="margin:0;font-size:.9em;color:#aaa;">{missing_text}</p>
            </div>
            """, unsafe_allow_html=True)

    with analysis_col:
        st.subheader("📊 SMC Confluence Analysis")
        breakdown = res.get("breakdown", {})
        analysis = res.get("analysis", {})
        if breakdown:
            st.markdown("**Active Confluences:**")
            for confluence, score in breakdown.items():
                if score > 0:
                    confluence_name = confluence.replace('_', ' ').title()
                    score_color = "#00b894" if score >= 0.3 else "#ffa726" if score >= 0.15 else "#ff6b6b"
                    st.markdown(f"""
                    <div class="confluence-item">
                        <div style="display:flex;justify-content:space-between;align-items:center;">
                            <span><strong>{confluence_name}</strong></span>
                            <span style="color:{score_color};font-weight:bold;">{score:.2f}</span>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                    st.progress(score)

        st.markdown("**Market Context:**")
        context_items = [
            ("Market Structure", analysis.get('market_structure', {}).get('label', 'None')),
            ("Sweep Detected", "Yes" if analysis.get('sweep_detected') else "No"),
            ("FVG Active", "Yes" if analysis.get('fvg_active') else "No"),
            ("Order Blocks", "Yes" if analysis.get('order_blocks_nearby') else "No"),
            ("HTF Proximity", "Yes" if analysis.get('htf_proximity') else "No"),
            ("Session Filter", "Yes" if analysis.get('session_optimal') else "No")
        ]
        for label, value in context_items:
            color = "#00b894" if value == "Yes" else "#ff6b6b" if value == "No" else "#ffa726"
            st.markdown(f"""
            <div style="display:flex;justify-content:space-between;padding:.3rem 0;border-bottom:1px solid rgba(255,255,255,0.1);">
                <span>{label}:</span>
                <span style="color:{color};font-weight:bold;">{value}</span>
            </div>
            """, unsafe_allow_html=True)

    st.divider()

    chart_col, htf_col = st.columns([2, 1])
    with chart_col:
        st.subheader("📈 Today's Price Action")
        if not df_today.empty:
            st.line_chart(df_today.set_index("time_et")["c"], height=300)
        else:
            st.info("No chart data available")
    with htf_col:
        st.subheader("📐 HTF Levels")
        htf_data = []
        if not h1.empty:
            htf_data.extend([
                {"TF":"H1","Type":"High","Level":f"${h1['h'].max():,.2f}","Dist":f"{abs(last_price - h1['h'].max()):,.0f}" if last_price else "—"},
                {"TF":"H1","Type":"Low","Level":f"${h1['l'].min():,.2f}","Dist":f"{abs(last_price - h1['l'].min()):,.0f}" if last_price else "—"}
            ])
        if not h4.empty:
            htf_data.extend([
                {"TF":"H4","Type":"High","Level":f"${h4['h'].max():,.2f}","Dist":f"{abs(last_price - h4['h'].max()):,.0f}" if last_price else "—"},
                {"TF":"H4","Type":"Low","Level":f"${h4['l'].min():,.2f}","Dist":f"{abs(last_price - h4['l'].min()):,.0f}" if last_price else "—"}
            ])
        if htf_data:
            st.dataframe(pd.DataFrame(htf_data), use_container_width=True, hide_index=True)
        else:
            st.info("No HTF data")

# ---------- bot control ----------
with tab_bot:
    st.markdown("""
    <style>
    .bot-header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:2rem;border-radius:15px;color:white;text-align:center;margin-bottom:2rem;box-shadow:0 8px 32px rgba(0,0,0,0.1)}
    .status-card{background:rgba(255,255,255,0.05);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);padding:1.5rem;border-radius:12px;text-align:center;height:120px;display:flex;flex-direction:column;justify-content:center}
    </style>
    """, unsafe_allow_html=True)

    if "last_refresh" not in st.session_state:
        st.session_state.last_refresh = datetime.now()

    current_price = float(df_today["c"].iloc[-1]) if not df_today.empty else None
    market_status = get_market_status()

    # Defaults to avoid NameError later
    signal = None
    confidence = 0.0

    if current_price and not df_today.empty:
        bot_analysis = strat.evaluate(
            current_price,
            df_today[["o", "h", "l", "c", "v"]],
            {"pdh": pdh, "pdl": pdl, "session_hi": ny_hi, "session_lo": ny_lo,
             "asia_hi": asia_hi, "asia_lo": asia_lo, "london_hi": london_hi, "london_lo": london_lo},
            meta,
            guard,
        )
        signal = bot_analysis.get("signal")
        confidence = bot_analysis.get("confidence", 0.0)
    else:
        bot_analysis = {}

    bot_status = "ACTIVE" if st.session_state.bot_active else "INACTIVE"
    # Check for override first
    if "market_status_override" in st.session_state:
        market_status = st.session_state.market_status_override
    status_color = "#00b894" if st.session_state.bot_active and market_status == "open" else "#ff6b6b" if not st.session_state.bot_active else "#ffa726"
    st.markdown(f"""
    <div class="bot-header">
        <h1>🤖 Trading Bot Control Center</h1>
        <h3>Status: <span style="color:{status_color};">{bot_status}</span> | Market: {market_status.upper()}</h3>
        <p>Enhanced SMC Strategy • Real-time Confluence Monitoring</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        runtime_str = "00:00:00"
        if st.session_state.bot_active and st.session_state.bot_start_time:
            runtime = datetime.now() - st.session_state.bot_start_time
            runtime_str = str(runtime).split('.')[0]
        st.markdown(f'<div class="status-card"><h4>⏱️ Runtime</h4><h3>{runtime_str}</h3></div>', unsafe_allow_html=True)
    with col2:
        st.markdown(f'<div class="status-card"><h4>📊 Market Scans</h4><h3>{st.session_state.bot_scan_count}</h3></div>', unsafe_allow_html=True)
    with col3:
        now_et = datetime.now(ET)
        st.markdown(f'<div class="status-card"><h4>🕐 Market Time</h4><h3>{now_et.strftime("%H:%M:%S")}</h3></div>', unsafe_allow_html=True)
    with col4:
        price_display = f"${current_price:.2f}" if current_price else "N/A"
        st.markdown(f'<div class="status-card"><h4>💰 Current Price</h4><h3>{price_display}</h3></div>', unsafe_allow_html=True)

    st.divider()
    st.subheader("🔍 Live Confluence Monitor")

    if current_price and bot_analysis:
        analysis = bot_analysis.get("analysis", {})
        breakdown = bot_analysis.get("breakdown", {})

        conf_col1, conf_col2, conf_col3 = st.columns(3)
        with conf_col1:
            st.markdown("**🔍 Detection Systems**")
            sweep_score = breakdown.get("sweep", 0.0)
            st.markdown(f"• Liquidity Sweep: {'🟢 Active' if sweep_score > 0 else '⚪ Inactive'}")
            if sweep_score > 0:
                st.progress(min(sweep_score, 1.0))
            mss_score = breakdown.get("mss", 0.0)
            st.markdown(f"• Market Structure: {'🟢 Active' if mss_score > 0 else '⚪ Inactive'}")
            if mss_score > 0:
                st.progress(min(mss_score, 1.0))
        with conf_col2:
            st.markdown("**📊 Price Analysis**")
            fvg_score = breakdown.get("fvg", 0.0)
            st.markdown(f"• Fair Value Gap: {'🟢 Active' if fvg_score > 0 else '⚪ Inactive'}")
            if fvg_score > 0:
                st.progress(min(fvg_score, 1.0))
            ob_score = breakdown.get("order_block", 0.0)
            st.markdown(f"• Order Blocks: {'🟢 Active' if ob_score > 0 else '⚪ Inactive'}")
            if ob_score > 0:
                st.progress(min(ob_score, 1.0))
        with conf_col3:
            st.markdown("**🎯 Positioning**")
            htf_score = breakdown.get("htf_bias", 0.0)
            st.markdown(f"• HTF Bias: {'🟢 Aligned' if htf_score > 0 else '⚪ Neutral'}")
            if htf_score > 0:
                st.progress(min(htf_score, 1.0))
            prox_score = breakdown.get("proximity", 0.0)
            st.markdown(f"• Level Proximity: {'🟢 Near Level' if prox_score > 0 else '⚪ Away'}")
            if prox_score > 0:
                st.progress(min(prox_score, 1.0))

        st.divider()
        if signal:
            signal_color = "#00b894" if signal == "long" else "#ff6b6b"
            signal_emoji = "🚀" if signal == "long" else "🔻"
            st.markdown(f"""
            <div style="background:linear-gradient(135deg,{signal_color} 0%,{signal_color}dd 100%);padding:1.5rem;border-radius:12px;color:white;text-align:center;margin:1rem 0;">
                <h3>{signal_emoji} {signal.upper()} SIGNAL ACTIVE</h3>
                <p>Confidence: {confidence:.1%} | {'TRADEABLE' if confidence >= 0.55 else 'MONITORING'}</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div style="background:rgba(255,255,255,0.1);padding:1.5rem;border-radius:12px;text-align:center;margin:1rem 0;">
                <h3>🔍 NO SIGNAL</h3>
                <p>Waiting for confluence alignment...</p>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.warning("⚠️ No market data available for confluence monitoring")

    st.divider()
    st.subheader("⚙️ Bot Controls")
    control_col1, control_col2, control_col3 = st.columns(3)
    with control_col1:
        # CRITICAL FIX: Add key to prevent double-click issues
        bot_button_key = f"bot_toggle_{st.session_state.get('bot_button_counter', 0)}"

        if st.button("🚀 Start Bot" if not st.session_state.bot_active else "⏹️ Stop Bot",
                     type=("primary" if not st.session_state.bot_active else "secondary"),
                     use_container_width=True,
                     key=bot_button_key):

            # Increment counter to ensure unique keys
            st.session_state.bot_button_counter = st.session_state.get('bot_button_counter', 0) + 1

            # Toggle bot state
            st.session_state.bot_active = not st.session_state.bot_active

            if st.session_state.bot_active:
                st.session_state.bot_start_time = datetime.now()
                st.session_state.bot_scan_count = 0
                st.session_state.bot_log.append(f"{datetime.now().strftime('%H:%M:%S')} - 🚀 Bot STARTED")
                st.session_state.bot_log.append(f"{datetime.now().strftime('%H:%M:%S')} - 📊 Monitoring {contract.get('symbol', 'Unknown')} on account {account.get('name', account_id)}")
                st.success("🟢 Bot activated!")
            else:
                st.session_state.bot_log.append(f"{datetime.now().strftime('%H:%M:%S')} - ⏹️ Bot STOPPED")
                st.success("🔴 Bot deactivated!")

            # Force rerun to update UI
            st.rerun()
    with control_col2:
        if st.button("📊 Force Scan", use_container_width=True):
            if current_price and bot_analysis:
                st.session_state.bot_scan_count += 1
                st.session_state.bot_log.append(f"{datetime.now().strftime('%H:%M:%S')} - 🔍 Manual scan #{st.session_state.bot_scan_count}")
                st.success("Scan completed!")
                st.rerun()
    with control_col3:
        if st.button("🧹 Clear Logs", use_container_width=True):
            st.session_state.bot_log = []
            st.success("Logs cleared!")
            st.rerun()

    st.subheader("📋 Bot Activity Log")
    if st.session_state.bot_log:
        log_text = "\n".join(st.session_state.bot_log[-50:])
        st.text_area("Recent Activity", log_text, height=400, disabled=True)
    else:
        st.info("No bot activity yet. Start the bot to begin monitoring.")

    if st.session_state.bot_active:
        now = datetime.now()
        # Check for override first
        current_market_status = st.session_state.get('market_status_override', market_status)
        refresh_interval = 3 if current_market_status == "open" else 15
        if (now - st.session_state.last_refresh).seconds >= refresh_interval:
            st.session_state.last_refresh = now
            if current_market_status == "open":
                st.session_state.bot_scan_count += 1
                if st.session_state.bot_scan_count % 5 == 1:
                    st.session_state.bot_log.append(
                        f"{datetime.now().strftime('%H:%M:%S')} - 🔎 Market scan #{st.session_state.bot_scan_count} | Price: ${current_price:.2f}"
                    )
                if signal:
                    st.session_state.bot_log.append(
                        f"{datetime.now().strftime('%H:%M:%S')} - 🎯 {signal.upper()} signal detected | Confidence: {confidence:.1%}"
                    )
                if signal and confidence >= 0.55 and market_status == "open":
                    try:
                        suggested = bot_analysis.get("suggested", {})
                        if suggested:
                            out = broker.place_bracket_idem(
                                account_id=account_id,
                                contract_id=contract_id,
                                side=("long" if signal == "long" else "short"),
                                qty=int(suggested["qty"]),
                                entry_type="MKT",
                                entry_price=None,
                                stop_price=float(suggested["stop"]),
                                target_price=float(suggested["target"]),
                                tag="bot",
                            )
                            trade_log = {
                                "time": datetime.now(),
                                "signal": signal,
                                "confidence": bot_analysis["confidence"],
                                "entry": current_price,
                                "stop": suggested["stop"],
                                "target": suggested["target"],
                                "qty": suggested["qty"],
                                "source": "bot"
                            }
                            st.session_state.bot_trades.append(trade_log)
                            st.session_state.bot_log.append(
                                f"{datetime.now().strftime('%H:%M:%S')} - ✅ Trade executed successfully! Order ID: {out.get('orderId', 'Unknown')}"
                            )
                            st.success("Bot trade executed!")
                            st.json(out)
                    except Exception as e:
                        st.error(f"Bot trade failed: {e}")
                        st.session_state.bot_log.append(f"{datetime.now().strftime('%H:%M:%S')} - ❌ Trade execution FAILED: {e}")
            st.rerun()

# ---------- replay ----------
with tab_replay:
    st.header("Replay / Backtest")
    day = st.date_input("Day", datetime.now(ET).date() - timedelta(days=1))
    min_conf_r = 0.55
    max_ct_r = st.number_input("Replay Max Contracts", 1, 100, 5, 1, key="replay_maxc")
    risk_per_trade_r = st.number_input("Replay Risk per trade ($)", 0.0, 10000.0, 100.0, 25.0, key="replay_risk")

    st.subheader("Replay SMC Confluences")
    col1, col2, col3 = st.columns(3)
    with col1:
        use_sweep_r = st.checkbox("Sweep Detection", True, key="replay_sweep")
        use_mss_r = st.checkbox("Market Structure", True, key="replay_mss")
        use_fvg_r = st.checkbox("Fair Value Gaps", True, key="replay_fvg")
    with col2:
        use_order_blocks_r = st.checkbox("Order Blocks", True, key="replay_ob")
        use_htf_bias_r = st.checkbox("HTF Bias", True, key="replay_htf")
        use_proximity_r = st.checkbox("Level Proximity", True, key="replay_prox")
    with col3:
        use_session_filter_r = st.checkbox("Session Filter", True, key="replay_session")

    st.subheader("Trading Hours (ET)")
    col1, col2 = st.columns(2)
    with col1:
        start_hour = st.selectbox("Start Hour", range(0, 24), index=8, key="start_hour")
    with col2:
        end_hour = st.selectbox("End Hour", range(0, 24), index=16, key="end_hour")

    if st.button("Run Replay"):
        with st.spinner("Loading data for replay..."):
            df_day = ensure_ohlcv(get_minute_bars_for_day(contract_id, day, live=False))
            df_prev = ensure_ohlcv(get_minute_bars_for_day(contract_id, day - timedelta(days=1), live=False))

        if df_day.empty:
            st.warning(f"No data for {day}. This could be a weekend, holiday, or the contract wasn't trading.")
            st.info("Try selecting a weekday when markets were open.")
            try:
                recent_data = get_minute_bars_for_day(contract_id, today, live=False)
                if not recent_data.empty:
                    st.success(f"✅ Data available for today ({today})")
                else:
                    st.warning("⚠️ No recent data available - check broker connection")
            except Exception as e:
                st.error(f"Error checking data availability: {e}")
        else:
            if 'time_et' in df_day.columns:
                df_day['time_et'] = pd.to_datetime(df_day['time_et'])
            try:
                df_day_filtered = df_day[(df_day["time_et"].dt.hour >= start_hour) & (df_day["time_et"].dt.hour <= end_hour)].copy()
            except Exception as e:
                st.error(f"Error filtering data: {e}")
                df_day_filtered = df_day.copy()

            if df_day_filtered.empty:
                st.warning("No data in selected time range.")
            else:
                if strategies_available():
                    strat = strategy_create(
                        "Enhanced SMC",
                        min_confidence_to_trade=0.55,
                        use_sweep=use_sweep_r, use_mss=use_mss_r, use_fvg=use_fvg_r,
                        use_order_blocks=use_order_blocks_r, use_htf_bias=use_htf_bias_r,
                        use_proximity=use_proximity_r, use_session_filter=use_session_filter_r
                    )
                else:
                    from enhanced_smc import build as smc_build
                    strat = smc_build(
                        min_confidence_to_trade=0.55,
                        use_sweep=use_sweep_r, use_mss=use_mss_r, use_fvg=use_fvg_r,
                        use_order_blocks=use_order_blocks_r, use_htf_bias=use_htf_bias_r,
                        use_proximity=use_proximity_r, use_session_filter=use_session_filter_r
                    )

                pdh_r, pdl_r, sessions_r = compute_prev_day_levels(df_prev)
                ny_hi_r = sessions_r.get("New York", (None, None))[0] if sessions_r else None
                ny_lo_r = sessions_r.get("New York", (None, None))[1] if sessions_r else None
                meta = ContractMeta(tick_size=contract.get("tickSize", 0.25), tick_value=contract.get("tickValue", 5.0))
                strat.prime(meta=meta, htf_bias=None, htf_levels=[x for x in (pdh_r, pdl_r, ny_hi_r, ny_lo_r) if x is not None])

                res = run_replay(
                    strategy=strat,
                    minutes_df=df_day_filtered,
                    prev_day_df=df_prev,
                    meta=meta,
                    start_et=et_midnight(day) + timedelta(hours=start_hour),
                    end_et=et_midnight(day) + timedelta(hours=end_hour),
                    guardrails={"allow_long": True, "allow_short": True, "dollar_risk_per_trade": risk_per_trade_r, "max_contracts": max_ct_r},
                    confidence_min=min_conf_r,
                    max_contracts=max_ct_r,
                )
                st.subheader("Results")
                st.write(f"Gross P&L: ${res.gross_pl:,.2f}")
                st.write(f"Win rate: {res.win_rate:.1%}")
                st.write(f"Total trades: {len(res.trades)}")

                if res.trades:
                    trades_df = pd.DataFrame(res.trades)
                    formatted = trades_df.copy()
                    if 'time' in formatted.columns:
                        formatted['Time'] = formatted['time'].dt.strftime('%m/%d %I:%M %p')
                        formatted = formatted.drop('time', axis=1)
                    if 'entry' in formatted.columns:
                        formatted['Entry'] = formatted['entry'].apply(lambda x: f"${x:.2f}")
                        formatted = formatted.drop('entry', axis=1)
                    if 'exit' in formatted.columns:
                        formatted['Exit'] = formatted['exit'].apply(lambda x: f"${x:.2f}")
                        formatted = formatted.drop('exit', axis=1)
                    if 'pl' in formatted.columns:
                        formatted['P&L'] = formatted['pl']
                        formatted = formatted.drop('pl', axis=1)
                    if 'confidence' in formatted.columns:
                        formatted['Confidence'] = formatted['confidence'].apply(lambda x: f"{x:.2f}")
                        formatted = formatted.drop('confidence', axis=1)
                    if 'reason' in formatted.columns:
                        formatted['Exit Reason'] = formatted['reason'].apply(lambda x: "🎯 Take Profit" if x == "target" else "🛑 Stop Loss" if x == "stop" else str(x))
                        formatted = formatted.drop('reason', axis=1)
                    if 'confluences' in formatted.columns:
                        formatted['Entry Signals'] = formatted['confluences'].apply(
                            lambda x: x.replace('sweep', 'Sweep').replace('mss', 'MSS').replace('fvg', 'FVG')
                                      .replace('order_blocks', 'Order Blocks').replace('session', 'Session')
                                      .replace('poi', 'POI').replace('htf_bias', 'HTF Bias').title() if pd.notna(x) else ""
                        )
                        formatted = formatted.drop('confluences', axis=1)

                    column_renames = {'side': 'Side', 'size': 'Qty', 'trade_reasons': 'Trade Details', 'commission': 'Commission', 'gross_pl': 'Gross P&L'}
                    for old_col, new_col in column_renames.items():
                        if old_col in formatted.columns:
                            formatted[new_col] = formatted[old_col]
                            formatted = formatted.drop(old_col, axis=1)

                    if 'Commission' in formatted.columns:
                        formatted['Commission'] = formatted['Commission'].apply(lambda x: f"${x:.2f}")
                    if 'Gross P&L' in formatted.columns:
                        formatted['Gross P&L'] = formatted['Gross P&L'].apply(lambda x: f"${x:+.2f}")

                    preferred_order = ['Time', 'Side', 'Entry', 'Exit', 'Exit Reason', 'Qty', 'P&L', 'Confidence', 'Entry Signals', 'Trade Details']
                    final_columns = [c for c in preferred_order if c in formatted.columns]
                    final_columns += [c for c in formatted.columns if c not in final_columns]
                    formatted = formatted[final_columns]

                    def style_pnl(val):
                        if pd.isna(val):
                            return ''
                        try:
                            num_val = float(val)
                            if num_val > 0:
                                return 'color:#00ff00;font-weight:bold'
                            elif num_val < 0:
                                return 'color:#ff4444;font-weight:bold'
                            else:
                                return 'color:#888888'
                        except:
                            return ''

                    if 'P&L' in formatted.columns:
                        formatted['P&L_Display'] = formatted['P&L'].apply(lambda x: f"${x:+.2f}" if pd.notna(x) else "")
                        display_trades = formatted.copy()
                        display_trades['P&L'] = display_trades['P&L_Display']
                        display_trades = display_trades.drop('P&L_Display', axis=1)
                        styled_df = display_trades.style.applymap(
                            lambda x: style_pnl(x.replace('$', '').replace('+', '').replace(',', '') if isinstance(x, str) and '$' in x else x),
                            subset=['P&L']
                        )
                    else:
                        styled_df = formatted.style

                    st.dataframe(
                        styled_df,
                        use_container_width=True,
                        height=400,
                        column_config={
                            "Trade Details": st.column_config.TextColumn("Trade Details", width="large", help="Detailed trade reasoning and analysis (truncated for display)"),
                            "Entry Signals": st.column_config.TextColumn("Entry Signals", width="medium", help="Active confluences that triggered the trade"),
                            "Exit Reason": st.column_config.TextColumn("Exit Reason", width="small", help="How the trade was closed"),
                            "Time": st.column_config.TextColumn("Time", width="small"),
                            "Side": st.column_config.TextColumn("Side", width="small"),
                            "P&L": st.column_config.TextColumn("P&L", width="small", help="Profit/Loss for the trade (Green=Profit, Red=Loss)")
                        }
                    )

                    if not formatted.empty:
                        st.subheader("📋 Detailed Trade Analysis")
                        for idx, trade in formatted.iterrows():
                            pnl_val = trade.get('P&L', 0)
                            try:
                                pnl_num = float(pnl_val) if isinstance(pnl_val, (int, float)) else float(str(pnl_val).replace('$', '').replace('+', '').replace(',', ''))
                                pnl_color = "🟢" if pnl_num > 0 else "🔴" if pnl_num < 0 else "⚪"
                                pnl_display = f"${pnl_num:+.2f}"
                            except:
                                pnl_color = "⚪"
                                pnl_display = str(pnl_val)
                            with st.expander(f"Trade {idx + 1}: {trade.get('Time', 'N/A')} - {trade.get('Side', 'N/A')} - {trade.get('Exit Reason', 'N/A')} - {pnl_color} {pnl_display}"):
                                col1, col2 = st.columns(2)
                                with col1:
                                    st.write(f"**Entry:** {trade.get('Entry', 'N/A')}")
                                    st.write(f"**Exit:** {trade.get('Exit', 'N/A')}")
                                    st.write(f"**Quantity:** {trade.get('Qty', 'N/A')}")
                                    if pnl_num > 0:
                                        st.success(f"**P&L:** {pnl_display}")
                                    elif pnl_num < 0:
                                        st.error(f"**P&L:** {pnl_display}")
                                    else:
                                        st.info(f"**P&L:** {pnl_display}")
                                with col2:
                                    st.write(f"**Confidence:** {trade.get('Confidence', 'N/A')}")
                                    st.write(f"**Entry Signals:** {trade.get('Entry Signals', 'N/A')}")
                                    st.write(f"**Exit Reason:** {trade.get('Exit Reason', 'N/A')}")
                                if 'Trade Details' in trade and pd.notna(trade['Trade Details']):
                                    st.write("**Full Trade Analysis:**")
                                    original_details = trades_df.iloc[idx].get('trade_reasons', 'No details available')
                                    st.text_area("", original_details, height=100, disabled=True, key=f"trade_details_{idx}")

                if res.equity_curve:
                    ec = pd.DataFrame(res.equity_curve, columns=["time", "equity"]).set_index("time")
                    st.line_chart(ec)

# ---------- trading history ----------
with tab_history:
    st.header("📊 Trading History")

    # CRITICAL FIX: Lazy load to prevent startup API flooding
    load_history = st.toggle("Load trading history (reduces startup load)", value=False, key="load_history_toggle")

    if not load_history:
        st.info("👆 Enable the toggle above to load trading history data. This prevents API flooding during startup.")
    else:
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("Start Date", datetime.now().date() - timedelta(days=7))
        with col2:
            end_date = st.date_input("End Date", datetime.now().date())

        if st.button("Load Trading History"):
            try:
                historical_trades = broker.get_historical_trades(account_id, start_date, end_date)

                if historical_trades and len(historical_trades) > 0:
                    st.write("**Debug: Sample Trade Data Structure**")
                    st.json(historical_trades[0])

                if historical_trades:
                    trade_data = []
                    total_pnl = 0
                    bot_trades = 0
                    manual_trades = 0
                    for trade in historical_trades:
                        try:
                            pnl = float(trade.get("profitAndLoss", 0) or trade.get("pnl", 0) or trade.get("pl", 0) or trade.get("profit_loss", 0))
                        except (ValueError, TypeError):
                            pnl = 0.0
                        total_pnl += pnl
                        source = str(trade.get("tag", trade.get("source", "")) or "")
                        is_bot_trade = "bot" in source.lower()
                        bot_trades += 1 if is_bot_trade else 0
                        manual_trades += 0 if is_bot_trade else 1
                        trade_data.append({
                            "Date": (trade.get("creationTimestamp", trade.get("timestamp", "")) or "")[:10],
                            "Symbol": trade.get("symbol", trade.get("contractId", ""))[-3:] if trade.get("contractId") else "",
                            "Side": "Long" if trade.get("side") == 1 else "Short" if trade.get("side") == -1 else str(trade.get("side", "")),
                            "Qty": trade.get("size", trade.get("qty", trade.get("quantity", 0))),
                            "Price": f"${float(trade.get('price', 0)):.2f}",
                            "P&L": f"${pnl:+.2f}",
                            "Fees": f"${float(trade.get('fees', 0)):.2f}",
                            "Source": "🤖 Bot" if is_bot_trade else "👤 Manual"
                        })

                st.subheader("📈 Summary")
                sum_col1, sum_col2, sum_col3, sum_col4 = st.columns(4)
                with sum_col1:
                    st.metric("Total P&L", f"${total_pnl:+,.2f}")
                with sum_col2:
                    st.metric("Total Trades", len(historical_trades))
                with sum_col3:
                    st.metric("Bot Trades", bot_trades)
                with sum_col4:
                    st.metric("Manual Trades", manual_trades)

                st.subheader("📋 Trade Details")
                if trade_data:
                    trades_df = pd.DataFrame(trade_data)

                    def style_pnl_history(val):
                        if pd.isna(val) or not isinstance(val, str):
                            return ''
                        try:
                            num_str = val.replace('$', '').replace('+', '').replace(',', '')
                            num_val = float(num_str)
                            if num_val > 0:
                                return 'color:#00ff00;font-weight:bold'
                            elif num_val < 0:
                                return 'color:#ff4444;font-weight:bold'
                            else:
                                return 'color:#888888'
                        except:
                            return ''

                    styled_trades = trades_df.style.applymap(style_pnl_history, subset=['P&L'])
                    st.dataframe(styled_trades, use_container_width=True,
                                 column_config={"P&L": st.column_config.TextColumn("P&L", help="Profit/Loss (Green=Profit, Red=Loss)")})
                else:
                    st.info("No trade data to display")

                if len(trade_data) > 1:
                    st.subheader("📊 Daily P&L Chart")
                    daily_pnl: Dict[str, float] = {}
                    for trade in historical_trades:
                        date_str = trade.get("creationTimestamp", trade.get("timestamp", ""))
                        if date_str:
                            d = date_str[:10]
                            try:
                                pnl = float(trade.get("profitAndLoss", 0) or trade.get("pnl", 0) or trade.get("pl", 0) or trade.get("profit_loss", 0))
                            except (ValueError, TypeError):
                                pnl = 0.0
                            daily_pnl[d] = daily_pnl.get(d, 0) + pnl
                    if daily_pnl:
                        chart_data = pd.DataFrame(list(daily_pnl.items()), columns=["Date", "P&L"])
                        chart_data["Date"] = pd.to_datetime(chart_data["Date"])
                        st.line_chart(chart_data.set_index("Date"))
                else:
                    st.info("No trades found for the selected date range")
            except Exception as e:
                st.error(f"Failed to load trading history: {e}")

    if st.session_state.get("bot_trades"):
        st.subheader("🤖 Current Session Bot Trades")
        current_trades = []
        for trade in st.session_state.bot_trades:
            current_trades.append({
                "Time": trade["time"].strftime("%H:%M:%S"),
                "Signal": trade["signal"].upper(),
                "Confidence": f"{trade['confidence']:.2f}",
                "Entry": f"${trade['entry']:.2f}",
                "Stop": f"${trade['stop']:.2f}",
                "Target": f"${trade['target']:.2f}",
                "Qty": trade["qty"]
            })
        if current_trades:
            st.dataframe(current_trades, use_container_width=True)

# ---------- logs ----------
with tab_logs:
    st.subheader("Logs")
    if st.session_state.get("log"):
        st.text("\n".join(st.session_state.log[-500:]))
    else:
        st.caption("No logs yet.")
    if st.button("Clear logs"):
        st.session_state.log = []
        st.success("Logs cleared.")
